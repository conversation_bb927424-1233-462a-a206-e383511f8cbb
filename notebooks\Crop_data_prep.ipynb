{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Notebook for transforming raw cpdata to Mergable data\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Filter cpdata.csv to MergeFileCrop.cv\n", "### Filter fertilizer.csv to MergerFileFert.csv"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Reading the data \n", "\n", "crop_data_path = '../Data-raw/cpdata.csv'\n", "fertilizer_data_path = '../Data-raw/Fertilizer.csv'\n", "\n", "crop = pd.read_csv(crop_data_path)\n", "fert = pd.read_csv(fertilizer_data_path)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>temperature</th>\n", "      <th>humidity</th>\n", "      <th>ph</th>\n", "      <th>rainfall</th>\n", "      <th>label</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20.879744</td>\n", "      <td>82.002744</td>\n", "      <td>6.502985</td>\n", "      <td>202.935536</td>\n", "      <td>rice</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>21.770462</td>\n", "      <td>80.319644</td>\n", "      <td>7.038096</td>\n", "      <td>226.655537</td>\n", "      <td>rice</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>23.004459</td>\n", "      <td>82.320763</td>\n", "      <td>7.840207</td>\n", "      <td>263.964248</td>\n", "      <td>rice</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>26.491096</td>\n", "      <td>80.158363</td>\n", "      <td>6.980401</td>\n", "      <td>242.864034</td>\n", "      <td>rice</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>20.130175</td>\n", "      <td>81.604873</td>\n", "      <td>7.628473</td>\n", "      <td>262.717340</td>\n", "      <td>rice</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   temperature   humidity        ph    rainfall label\n", "0    20.879744  82.002744  6.502985  202.935536  rice\n", "1    21.770462  80.319644  7.038096  226.655537  rice\n", "2    23.004459  82.320763  7.840207  263.964248  rice\n", "3    26.491096  80.158363  6.980401  242.864034  rice\n", "4    20.130175  81.604873  7.628473  262.717340  rice"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["crop.head()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0</th>\n", "      <th>Crop</th>\n", "      <th>N</th>\n", "      <th>P</th>\n", "      <th>K</th>\n", "      <th>pH</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>Rice</td>\n", "      <td>80</td>\n", "      <td>40</td>\n", "      <td>40</td>\n", "      <td>5.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON>(Sorghum)</td>\n", "      <td>80</td>\n", "      <td>40</td>\n", "      <td>40</td>\n", "      <td>5.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON>(JAV)</td>\n", "      <td>70</td>\n", "      <td>40</td>\n", "      <td>45</td>\n", "      <td>5.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>80</td>\n", "      <td>40</td>\n", "      <td>20</td>\n", "      <td>5.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td><PERSON><PERSON>( na<PERSON><PERSON><PERSON>)</td>\n", "      <td>50</td>\n", "      <td>40</td>\n", "      <td>20</td>\n", "      <td>5.5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Unnamed: 0              Crop   N   P   K   pH\n", "0           0              Rice  80  40  40  5.5\n", "1           1    Jo<PERSON>(Sorghum)  80  40  40  5.5\n", "2           2       <PERSON><PERSON>(JAV)  70  40  45  5.5\n", "3           3             Maize  80  40  20  5.5\n", "4           4  Ra<PERSON>( naach<PERSON>ii)  50  40  20  5.5"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["fert.head()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Function for lowering the cases\n", "def change_case(i):\n", "    i = i.replace(\" \", \"\")\n", "    i = i.lower()\n", "    return i"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["fert['Crop'] = fert['Crop'].apply(change_case)\n", "crop['label'] = crop['label'].apply(change_case)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["#make some changes in ferttilizer dataset\n", "\n", "fert['Crop'] = fert['Crop'].replace('mungbeans','mungbean')\n", "fert['Crop'] = fert['Crop'].replace('lentils(masoordal)','lentil')\n", "fert['Crop'] = fert['Crop'].replace('pigeonpeas(toordal)','pigeonpeas')\n", "fert['Crop'] = fert['Crop'].replace('mothbean(matki)','mothbeans')\n", "fert['Crop'] = fert['Crop'].replace('chickpeas(channa)','chickpea')"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>temperature</th>\n", "      <th>humidity</th>\n", "      <th>ph</th>\n", "      <th>rainfall</th>\n", "      <th>label</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20.879744</td>\n", "      <td>82.002744</td>\n", "      <td>6.502985</td>\n", "      <td>202.935536</td>\n", "      <td>rice</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>21.770462</td>\n", "      <td>80.319644</td>\n", "      <td>7.038096</td>\n", "      <td>226.655537</td>\n", "      <td>rice</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>23.004459</td>\n", "      <td>82.320763</td>\n", "      <td>7.840207</td>\n", "      <td>263.964248</td>\n", "      <td>rice</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>26.491096</td>\n", "      <td>80.158363</td>\n", "      <td>6.980401</td>\n", "      <td>242.864034</td>\n", "      <td>rice</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>20.130175</td>\n", "      <td>81.604873</td>\n", "      <td>7.628473</td>\n", "      <td>262.717340</td>\n", "      <td>rice</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   temperature   humidity        ph    rainfall label\n", "0    20.879744  82.002744  6.502985  202.935536  rice\n", "1    21.770462  80.319644  7.038096  226.655537  rice\n", "2    23.004459  82.320763  7.840207  263.964248  rice\n", "3    26.491096  80.158363  6.980401  242.864034  rice\n", "4    20.130175  81.604873  7.628473  262.717340  rice"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["crop.head()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>temperature</th>\n", "      <th>humidity</th>\n", "      <th>ph</th>\n", "      <th>rainfall</th>\n", "      <th>label</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3095</th>\n", "      <td>25.287846</td>\n", "      <td>89.636679</td>\n", "      <td>6.765095</td>\n", "      <td>58.286977</td>\n", "      <td>watermelon</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3096</th>\n", "      <td>26.638386</td>\n", "      <td>84.695469</td>\n", "      <td>6.189214</td>\n", "      <td>48.324286</td>\n", "      <td>watermelon</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3097</th>\n", "      <td>25.331045</td>\n", "      <td>84.305338</td>\n", "      <td>6.904242</td>\n", "      <td>41.532187</td>\n", "      <td>watermelon</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3098</th>\n", "      <td>26.897502</td>\n", "      <td>83.892415</td>\n", "      <td>6.463271</td>\n", "      <td>43.971937</td>\n", "      <td>watermelon</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3099</th>\n", "      <td>26.986037</td>\n", "      <td>89.413849</td>\n", "      <td>6.260839</td>\n", "      <td>58.548767</td>\n", "      <td>watermelon</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      temperature   humidity        ph   rainfall       label\n", "3095    25.287846  89.636679  6.765095  58.286977  watermelon\n", "3096    26.638386  84.695469  6.189214  48.324286  watermelon\n", "3097    25.331045  84.305338  6.904242  41.532187  watermelon\n", "3098    26.897502  83.892415  6.463271  43.971937  watermelon\n", "3099    26.986037  89.413849  6.260839  58.548767  watermelon"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["crop.tail()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['rice', 'wheat', 'mungbean', 'tea', 'millet', 'maize', 'lentil',\n", "       'jute', 'coffee', 'cotton', 'groundnut', 'peas', 'rubber',\n", "       'sugarcane', 'tobacco', 'kidneybeans', 'mothbeans', 'coconut',\n", "       'blackgram', 'adzukibeans', 'pigeonpeas', 'chickpea', 'banana',\n", "       'grapes', 'apple', 'mango', 'muskmelon', 'orange', 'papaya',\n", "       'pomegranate', 'watermelon'], dtype=object)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["crop_names = crop['label'].unique()\n", "crop_names"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0</th>\n", "      <th>Crop</th>\n", "      <th>N</th>\n", "      <th>P</th>\n", "      <th>K</th>\n", "      <th>pH</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>rice</td>\n", "      <td>80</td>\n", "      <td>40</td>\n", "      <td>40</td>\n", "      <td>5.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>jowar(sorghum)</td>\n", "      <td>80</td>\n", "      <td>40</td>\n", "      <td>40</td>\n", "      <td>5.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>barley(jav)</td>\n", "      <td>70</td>\n", "      <td>40</td>\n", "      <td>45</td>\n", "      <td>5.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>maize</td>\n", "      <td>80</td>\n", "      <td>40</td>\n", "      <td>20</td>\n", "      <td>5.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td>ragi(na<PERSON>nnii)</td>\n", "      <td>50</td>\n", "      <td>40</td>\n", "      <td>20</td>\n", "      <td>5.5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Unnamed: 0             Crop   N   P   K   pH\n", "0           0             rice  80  40  40  5.5\n", "1           1   jowar(sorghum)  80  40  40  5.5\n", "2           2      barley(jav)  70  40  45  5.5\n", "3           3            maize  80  40  20  5.5\n", "4           4  ragi(na<PERSON>nnii)  50  40  20  5.5"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["fert.head()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["del fert['Unnamed: 0']"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['rice', 'jowar(sorghum)', 'barley(jav)', 'maize',\n", "       'ragi(na<PERSON><PERSON><PERSON>)', 'chickpea', 'frenchbeans(farasbi)',\n", "       'favabeans(papdi-val)', 'limabeans(pavta)', 'clusterbeans(gavar)',\n", "       'soyabean', 'blackeyedbeans(chawli)', 'kidneybeans', 'pigeonpeas',\n", "       'mothbeans', 'mungbean', 'greenpeas', 'horsegram(kulthi)',\n", "       'blackgram', 'rapeseed(mohri)', 'corianderseeds', 'mustardseeds',\n", "       'sesameseed', 'cuminseeds', 'lentil', 'brinjal', 'beetroot',\n", "       'bittergourd', 'bottlegourd', 'capsicum', 'cabbage', 'carrot',\n", "       'cauliflower', 'cucumber', 'corianderleaves', 'curryleaves',\n", "       'drumstick–moringa', 'chili', 'ladyfinger', 'mushroom', 'onion',\n", "       'potato', 'pumpkin', 'radish', 'olive', 'sweetpotato',\n", "       'fenugreekleaf(methi)', 'spinach', 'ridgegourd',\n", "       'gooseberry(amla)', 'jambun(syzygiumcumini)',\n", "       'ziz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(bor)', 'gar<PERSON><PERSON><PERSON><PERSON>(kokam)', 'tamarind',\n", "       'tapioca(suran)', 'garlic', 'lemon', 'tomato', 'ashgourd',\n", "       'pineapple', 'pomegranate', 'banana', 'mango', 'grapes',\n", "       'jackfruit', 'guava', 'watermelon', 'muskmelon', 'apricot',\n", "       'apple', 'chickoo', 'custardapple', 'dates', 'figs', 'orange',\n", "       'papaya', 'aniseed', 'asafoetida', 'bayleaf', 'blackpepper',\n", "       'cardamom', 'cinnamon', 'cloves', 'jaiphal(nutmeg)', 'ginger',\n", "       'turmeric', 'cashewnuts', 'raisins', 'coconut', 'almondnut',\n", "       'arecanut', 'pistachionut', 'lemongrass', 'cotton', 'jute',\n", "       'coffee', 'sunflower'], dtype=object)"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["crop_names_from_fert = fert['Crop'].unique()\n", "crop_names_from_fert"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    temperature   humidity        ph    rainfall label\n", "0     20.879744  82.002744  6.502985  202.935536  rice\n", "1     21.770462  80.319644  7.038096  226.655537  rice\n", "2     23.004459  82.320763  7.840207  263.964248  rice\n", "3     26.491096  80.158363  6.980401  242.864034  rice\n", "4     20.130175  81.604873  7.628473  262.717340  rice\n", "..          ...        ...       ...         ...   ...\n", "95    22.683191  83.463583  6.604993  194.265172  rice\n", "96    21.533463  82.140041  6.500343  295.924880  rice\n", "97    21.408658  83.329319  5.935745  287.576694  rice\n", "98    26.543481  84.673536  7.072656  183.622266  rice\n", "99    23.359054  83.595123  5.333323  188.413665  rice\n", "\n", "[100 rows x 5 columns]\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "     temperature   humidity        ph    rainfall  label\n", "500    22.613600  63.690706  5.749914   87.759539  maize\n", "501    26.100184  71.574769  6.931757  102.266244  maize\n", "502    23.558821  71.593514  6.657965   66.719955  maize\n", "503    19.972160  57.682729  6.596061   60.651715  maize\n", "504    18.478913  62.695039  5.970458   65.438354  maize\n", "..           ...        ...       ...         ...    ...\n", "595    18.928519  72.800861  6.158860   82.341629  maize\n", "596    23.305468  63.246480  6.385684  108.760300  maize\n", "597    18.748267  62.498785  6.417820   70.234016  maize\n", "598    19.742133  59.662631  6.381202   65.508614  maize\n", "599    25.730444  70.747393  6.877869   98.737713  maize\n", "\n", "[100 rows x 5 columns]\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "      temperature   humidity        ph   rainfall     label\n", "2100    17.024985  16.988612  7.485996  88.551231  chickpea\n", "2101    19.020613  17.131591  6.920251  79.926981  chickpea\n", "2102    17.887765  15.405897  5.996932  68.549329  chickpea\n", "2103    18.868056  15.658092  6.391174  88.510490  chickpea\n", "2104    18.369526  19.563810  7.152811  79.263577  chickpea\n", "...           ...        ...       ...        ...       ...\n", "2195    17.341502  18.756263  8.861480  67.954543  chickpea\n", "2196    17.437327  14.338474  7.861128  73.092670  chickpea\n", "2197    18.897802  19.761829  7.452671  69.095125  chickpea\n", "2198    18.591908  14.779596  7.168096  89.609825  chickpea\n", "2199    18.315615  15.361435  7.263119  81.787105  chickpea\n", "\n", "[100 rows x 5 columns]\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "      temperature   humidity        ph    rainfall        label\n", "1500    17.136928  20.595417  5.685972  128.256862  kidneybeans\n", "1501    19.634743  18.907056  5.759237  106.359818  kidneybeans\n", "1502    22.913502  21.339531  5.873172  109.225556  kidneybeans\n", "1503    16.433403  24.240459  5.926677  140.371781  kidneybeans\n", "1504    22.139747  23.022511  5.955617   76.641283  kidneybeans\n", "...           ...        ...       ...         ...          ...\n", "1595    20.109938  23.223238  5.595032   73.363865  kidneybeans\n", "1596    23.605066  21.905396  5.525905  100.597873  kidneybeans\n", "1597    19.731369  24.894874  5.819404   84.063541  kidneybeans\n", "1598    20.934099  21.189301  5.562202  133.191442  kidneybeans\n", "1599    18.782263  20.247683  5.630665  104.257072  kidneybeans\n", "\n", "[100 rows x 5 columns]\n", "      temperature   humidity        ph    rainfall       label\n", "2000    36.512684  57.928872  6.031608  122.653969  pigeonpeas\n", "2001    36.891637  62.731782  5.269085  163.726655  pigeonpeas\n", "2002    29.235405  59.389676  5.985793  103.330180  pigeonpeas\n", "2003    27.335349  43.357960  6.091863  142.330368  pigeonpeas\n", "2004    21.064368  55.469859  5.624731  184.622671  pigeonpeas\n", "...           ...        ...       ...         ...         ...\n", "2095    29.892866  66.353751  6.931925  198.140300  pigeonpeas\n", "2096    29.377356  44.822946  6.842744  172.401680  pigeonpeas\n", "2097    29.650529  42.898332  6.876573  186.922605  pigeonpeas\n", "2098    19.542849  66.347773  6.151029  173.110698  pigeonpeas\n", "2099    20.046118  48.939056  4.567446  122.456420  pigeonpeas\n", "\n", "[100 rows x 5 columns]\n", "      temperature   humidity        ph   rainfall      label\n", "1600    27.910952  64.709306  3.692864  32.678919  mothbeans\n", "1601    27.322206  51.278688  4.371746  36.503791  mothbeans\n", "1602    28.660242  59.318912  8.399136  36.926297  mothbeans\n", "1603    29.029553  61.093875  8.840656  72.980166  mothbeans\n", "1604    27.780315  54.650300  8.153023  32.050253  mothbeans\n", "...           ...        ...       ...        ...        ...\n", "1695    29.337434  49.003231  8.914075  42.440543  mothbeans\n", "1696    27.965837  61.349001  8.639586  70.104721  mothbeans\n", "1697    24.868040  48.275320  8.621514  63.918765  mothbeans\n", "1698    25.876823  45.963419  5.838509  38.532547  mothbeans\n", "1699    31.019636  49.976752  3.532009  32.812965  mothbeans\n", "\n", "[100 rows x 5 columns]\n", "     temperature   humidity        ph   rainfall     label\n", "200    27.433294  87.805077  7.185301  54.733676  mungbean\n", "201    28.334043  80.772760  7.034214  38.797641  mungbean\n", "202    27.014704  84.342627  6.635969  55.296354  mungbean\n", "203    28.174327  81.045548  6.828187  36.357207  mungbean\n", "204    29.878881  87.327612  6.890780  44.752159  mungbean\n", "..           ...        ...       ...        ...       ...\n", "295    28.727527  89.127604  7.069748  58.529743  mungbean\n", "296    27.956397  83.527060  6.921994  43.257268  mungbean\n", "297    28.174587  83.696593  6.770955  37.246465  mungbean\n", "298    28.776535  86.691340  6.983130  56.124432  mungbean\n", "299    28.438097  83.489914  6.267684  52.554700  mungbean\n", "\n", "[100 rows x 5 columns]\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "      temperature   humidity        ph   rainfall      label\n", "1800    29.484400  63.199153  7.454532  71.890907  blackgram\n", "1801    26.734340  68.139997  7.040056  67.150964  blackgram\n", "1802    26.272744  62.288149  7.418651  70.232076  blackgram\n", "1803    34.036792  67.211138  6.501869  73.235736  blackgram\n", "1804    28.036441  65.066017  6.814411  72.495077  blackgram\n", "...           ...        ...       ...        ...        ...\n", "1895    33.369844  65.677182  6.874142  64.895175  blackgram\n", "1896    31.434506  62.993035  7.760618  64.776515  blackgram\n", "1897    27.716783  63.291034  6.781842  68.565080  blackgram\n", "1898    32.639187  61.300905  7.326980  61.838761  blackgram\n", "1899    32.747739  67.779546  7.453975  63.377844  blackgram\n", "\n", "[100 rows x 5 columns]\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "     temperature   humidity        ph   rainfall   label\n", "600    28.051536  63.498022  7.604110  43.357954  lentil\n", "601    19.440843  63.277715  7.728832  46.831301  lentil\n", "602    29.848231  60.638726  7.491217  46.804526  lentil\n", "603    21.363838  69.923759  6.633865  46.635286  lentil\n", "604    26.286639  68.519667  7.324863  46.138330  lentil\n", "..           ...        ...       ...        ...     ...\n", "695    23.052764  60.424786  7.011121  52.602853  lentil\n", "696    21.658458  63.583371  6.280726  38.076594  lentil\n", "697    26.250703  67.627797  7.621495  40.810630  lentil\n", "698    20.971953  63.831799  7.630424  53.102079  lentil\n", "699    23.897364  66.321020  7.802212  40.745368  lentil\n", "\n", "[100 rows x 5 columns]\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "      temperature   humidity        ph    rainfall        label\n", "2900    24.559816  91.635362  5.922936  111.968462  pomegranate\n", "2901    19.656901  89.937010  5.937650  108.045893  pomegranate\n", "2902    18.783596  87.402477  6.804781  102.518476  pomegranate\n", "2903    24.146963  94.511066  6.424671  110.231663  pomegranate\n", "2904    22.445813  89.901470  6.738016  109.390600  pomegranate\n", "...           ...        ...       ...         ...          ...\n", "2995    20.002190  85.836182  7.116539  112.337046  pomegranate\n", "2996    19.851393  89.807323  6.430163  102.818636  pomegranate\n", "2997    21.254336  92.650589  7.159521  106.278467  pomegranate\n", "2998    23.653741  93.326575  6.431266  109.807618  pomegranate\n", "2999    23.884048  86.206138  6.082572  108.312179  pomegranate\n", "\n", "[100 rows x 5 columns]\n", "      temperature   humidity        ph    rainfall   label\n", "2200    29.367924  76.249001  6.149934   92.828409  banana\n", "2201    27.333690  83.676752  5.849076  101.049479  banana\n", "2202    27.400536  82.962213  6.276800  104.937800  banana\n", "2203    29.315908  80.115857  5.926825   90.109781  banana\n", "2204    26.054330  79.396545  5.519088  113.229737  banana\n", "...           ...        ...       ...         ...     ...\n", "2295    27.359116  84.546250  6.387431   90.812505  banana\n", "2296    28.010680  76.528081  5.891414  103.704078  banana\n", "2297    28.672089  82.207936  5.725419   94.379875  banana\n", "2298    27.345851  78.487383  6.281070   92.155243  banana\n", "2299    29.507046  78.205856  5.507642   98.125658  banana\n", "\n", "[100 rows x 5 columns]\n", "      temperature   humidity        ph    rainfall  label\n", "2500    29.737700  47.548852  5.954627   90.095869  mango\n", "2501    33.556956  53.729798  4.757115   98.675276  mango\n", "2502    27.003155  47.675254  5.699587   95.851183  mango\n", "2503    33.561502  45.535566  5.977414   95.705259  mango\n", "2504    35.898556  54.259642  6.430139   92.197217  mango\n", "...           ...        ...       ...         ...    ...\n", "2595    31.484517  48.779263  4.525722   93.172220  mango\n", "2596    27.698193  51.415932  5.403908  100.772070  mango\n", "2597    30.412358  52.481006  6.621624   93.923759  mango\n", "2598    32.177520  54.013527  6.207496   91.887661  mango\n", "2599    32.611261  47.749165  5.418475   91.101908  mango\n", "\n", "[100 rows x 5 columns]\n", "      temperature   humidity        ph   rainfall   label\n", "2300    29.996772  81.541566  6.112306  67.125345  grapes\n", "2301    30.728040  82.426141  6.092242  68.381355  grapes\n", "2302    32.445778  83.885049  5.896343  68.739325  grapes\n", "2303    37.465668  80.659687  6.155261  66.838723  grapes\n", "2304    22.032962  83.743728  5.732454  65.344408  grapes\n", "...           ...        ...       ...        ...     ...\n", "2395     9.851243  80.226317  5.965379  68.428024  grapes\n", "2396    24.972561  82.728287  6.476758  66.700163  grapes\n", "2397    27.237083  82.945733  6.224543  70.425089  grapes\n", "2398    18.706791  83.479529  6.209928  66.596449  grapes\n", "2399     9.949929  82.551390  5.841138  66.008176  grapes\n", "\n", "[100 rows x 5 columns]\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "      temperature   humidity        ph   rainfall       label\n", "3000    26.473302  80.922544  6.283818  53.657426  watermelon\n", "3001    25.187800  83.446217  6.818261  46.874209  watermelon\n", "3002    25.299547  81.775276  6.376201  57.041471  watermelon\n", "3003    24.746313  88.308663  6.581588  57.958261  watermelon\n", "3004    26.587407  81.325632  6.932740  41.875400  watermelon\n", "...           ...        ...       ...        ...         ...\n", "3095    25.287846  89.636679  6.765095  58.286977  watermelon\n", "3096    26.638386  84.695469  6.189214  48.324286  watermelon\n", "3097    25.331045  84.305338  6.904242  41.532187  watermelon\n", "3098    26.897502  83.892415  6.463271  43.971937  watermelon\n", "3099    26.986037  89.413849  6.260839  58.548767  watermelon\n", "\n", "[100 rows x 5 columns]\n", "      temperature   humidity        ph   rainfall      label\n", "2600    27.578269  94.118782  6.776533  28.082532  muskmelon\n", "2601    27.820548  93.035552  6.528404  26.324055  muskmelon\n", "2602    29.099104  94.222378  6.750146  22.524973  muskmelon\n", "2603    28.049436  90.831307  6.562833  20.762230  muskmelon\n", "2604    29.916906  94.556956  6.117530  28.160572  muskmelon\n", "...           ...        ...       ...        ...        ...\n", "2695    29.527531  94.574594  6.700338  21.135457  muskmelon\n", "2696    28.504164  93.468065  6.565313  24.200072  muskmelon\n", "2697    28.895786  94.789930  6.286515  23.036250  muskmelon\n", "2698    27.049275  91.382173  6.448062  23.657475  muskmelon\n", "2699    28.960179  91.695322  6.585873  24.745820  muskmelon\n", "\n", "[100 rows x 5 columns]\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "      temperature   humidity        ph    rainfall  label\n", "2400    22.750888  90.694892  5.521467  110.431786  apple\n", "2401    23.849401  94.348150  6.133221  114.051250  apple\n", "2402    22.608010  94.589006  6.226290  116.039659  apple\n", "2403    21.186674  91.134357  6.321152  122.233323  apple\n", "2404    23.410447  91.699133  5.587906  116.077793  apple\n", "...           ...        ...       ...         ...    ...\n", "2495    23.805938  92.488795  5.889481  119.633555  apple\n", "2496    22.319441  90.851744  5.732758  100.117344  apple\n", "2497    22.144641  93.825674  6.400321  120.631078  apple\n", "2498    23.651676  94.505288  6.496934  115.361127  apple\n", "2499    22.169395  90.271856  6.229499  124.468311  apple\n", "\n", "[100 rows x 5 columns]\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "      temperature   humidity        ph    rainfall   label\n", "2700    15.781442  92.510777  6.354007  119.035002  orange\n", "2701    26.030973  91.508193  7.511755  101.284774  orange\n", "2702    13.360506  91.356082  7.335158  111.226688  orange\n", "2703    18.879577  92.043045  7.813917  114.665951  orange\n", "2704    29.477417  91.578029  7.129137  111.172750  orange\n", "...           ...        ...       ...         ...     ...\n", "2795    32.717485  90.546083  7.656978  113.328978  orange\n", "2796    25.162966  92.547360  7.105905  114.311720  orange\n", "2797    27.681673  94.473169  7.199106  113.999515  orange\n", "2798    21.350934  90.949297  7.871063  107.086209  orange\n", "2799    11.698946  93.256389  7.566166  103.200599  orange\n", "\n", "[100 rows x 5 columns]\n", "      temperature   humidity        ph    rainfall   label\n", "2800    35.214628  91.497251  6.793245  243.074507  papaya\n", "2801    42.394134  90.790281  6.576261   88.466075  papaya\n", "2802    38.419163  91.142204  6.751453  119.265388  papaya\n", "2803    35.332949  92.115086  6.560743  235.613359  papaya\n", "2804    42.923253  90.076005  6.938313  196.240824  papaya\n", "...           ...        ...       ...         ...     ...\n", "2895    40.102077  94.351102  6.979102  149.119999  papaya\n", "2896    38.589545  91.580765  6.825665  102.270823  papaya\n", "2897    41.313301  91.150880  6.617067  239.742755  papaya\n", "2898    37.035519  91.794302  6.551893  188.518142  papaya\n", "2899    23.012402  91.073555  6.598860  208.335798  papaya\n", "\n", "[100 rows x 5 columns]\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "      temperature   humidity        ph    rainfall    label\n", "1700    26.762749  92.860569  6.420019  224.590366  coconut\n", "1701    25.612944  94.313884  5.740055  224.320676  coconut\n", "1702    28.130115  95.648076  5.686973  151.076190  coconut\n", "1703    25.028872  91.537209  6.293662  179.824894  coconut\n", "1704    27.797977  99.645730  6.381975  181.694228  coconut\n", "...           ...        ...       ...         ...      ...\n", "1795    28.435729  95.884041  5.665785  203.928371  coconut\n", "1796    28.940997  93.001090  5.764615  191.772309  coconut\n", "1797    26.454887  93.450426  5.901496  149.222026  coconut\n", "1798    25.794905  93.841506  5.779033  152.423871  coconut\n", "1799    26.931419  98.803136  5.671549  166.571288  coconut\n", "\n", "[100 rows x 5 columns]\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n", "     temperature   humidity        ph   rainfall   label\n", "900    24.402289  79.197320  7.231325  90.802236  cotton\n", "901    23.095956  84.862757  6.925412  71.295811  cotton\n", "902    23.965635  76.976967  7.633437  90.756167  cotton\n", "903    24.887381  75.621372  6.827355  89.760504  cotton\n", "904    25.362438  83.632761  6.176716  88.436189  cotton\n", "..           ...        ...       ...        ...     ...\n", "995    22.107190  78.583201  6.364730  74.941366  cotton\n", "996    23.038140  76.110215  6.913679  91.496975  cotton\n", "997    24.547953  75.397527  7.766260  63.880799  cotton\n", "998    23.738680  75.775038  7.556064  76.636692  cotton\n", "999    22.318719  83.861300  7.288377  65.357470  cotton\n", "\n", "[100 rows x 5 columns]\n", "     temperature   humidity        ph    rainfall label\n", "700    25.524690  72.248508  6.002525  151.886997  jute\n", "701    26.591050  82.941641  6.033485  161.247000  jute\n", "702    25.297818  86.887054  7.121934  196.624951  jute\n", "703    25.721009  88.165136  6.207460  175.608670  jute\n", "704    23.584193  72.004608  6.090060  190.424216  jute\n", "..           ...        ...       ...         ...   ...\n", "795    23.874845  86.792613  6.718725  177.514731  jute\n", "796    23.928879  88.071123  6.880205  154.660874  jute\n", "797    24.814412  81.686889  6.861069  190.788639  jute\n", "798    24.447439  82.286484  6.769346  190.968489  jute\n", "799    26.574217  73.819949  7.261581  159.322307  jute\n", "\n", "[100 rows x 5 columns]\n", "     temperature   humidity        ph    rainfall   label\n", "800    26.333780  57.364700  7.261314  191.654941  coffee\n", "801    26.452885  55.322227  7.235070  144.686134  coffee\n", "802    25.708227  52.886671  7.189156  136.732509  coffee\n", "803    24.128325  56.181077  6.431900  147.275782  coffee\n", "804    23.443723  60.395233  6.423211  122.210325  coffee\n", "..           ...        ...       ...         ...     ...\n", "895    26.774637  66.413269  6.780064  177.774507  coffee\n", "896    27.417112  56.636362  6.086922  127.924610  coffee\n", "897    24.131797  67.225123  6.362608  173.322839  coffee\n", "898    26.272418  52.127394  6.758793  127.175293  coffee\n", "899    23.603016  60.396475  6.779833  140.937041  coffee\n", "\n", "[100 rows x 5 columns]\n", "Empty DataFrame\n", "Columns: [temperature, humidity, ph, rainfall, label]\n", "Index: []\n"]}], "source": ["for i in crop_names_from_fert:\n", "    print(crop[crop['label'] == i])"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["0             rice\n", "1             rice\n", "2             rice\n", "3             rice\n", "4             rice\n", "           ...    \n", "3095    watermelon\n", "3096    watermelon\n", "3097    watermelon\n", "3098    watermelon\n", "3099    watermelon\n", "Name: label, Length: 3100, dtype: object"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["crop['label']"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["extract_labels = []\n", "for i in crop_names_from_fert:\n", "    if i in crop_names:\n", "        extract_labels.append(i)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["# using extract labesl on crop to get all the data related to those labels\n", "new_crop = pd.DataFrame(columns = crop.columns)\n", "new_fert = pd.DataFrame(columns = fert.columns)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["for label in extract_labels:\n", "    new_crop = new_crop.append(crop[crop['label'] == label])"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["for label in extract_labels:\n", "    new_fert = new_fert.append(fert[fert['Crop'] == label].iloc[0])"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>temperature</th>\n", "      <th>humidity</th>\n", "      <th>ph</th>\n", "      <th>rainfall</th>\n", "      <th>label</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20.879744</td>\n", "      <td>82.002744</td>\n", "      <td>6.502985</td>\n", "      <td>202.935536</td>\n", "      <td>rice</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>21.770462</td>\n", "      <td>80.319644</td>\n", "      <td>7.038096</td>\n", "      <td>226.655537</td>\n", "      <td>rice</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>23.004459</td>\n", "      <td>82.320763</td>\n", "      <td>7.840207</td>\n", "      <td>263.964248</td>\n", "      <td>rice</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>26.491096</td>\n", "      <td>80.158363</td>\n", "      <td>6.980401</td>\n", "      <td>242.864034</td>\n", "      <td>rice</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>20.130175</td>\n", "      <td>81.604873</td>\n", "      <td>7.628473</td>\n", "      <td>262.717340</td>\n", "      <td>rice</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>895</th>\n", "      <td>26.774637</td>\n", "      <td>66.413269</td>\n", "      <td>6.780064</td>\n", "      <td>177.774507</td>\n", "      <td>coffee</td>\n", "    </tr>\n", "    <tr>\n", "      <th>896</th>\n", "      <td>27.417112</td>\n", "      <td>56.636362</td>\n", "      <td>6.086922</td>\n", "      <td>127.924610</td>\n", "      <td>coffee</td>\n", "    </tr>\n", "    <tr>\n", "      <th>897</th>\n", "      <td>24.131797</td>\n", "      <td>67.225123</td>\n", "      <td>6.362608</td>\n", "      <td>173.322839</td>\n", "      <td>coffee</td>\n", "    </tr>\n", "    <tr>\n", "      <th>898</th>\n", "      <td>26.272418</td>\n", "      <td>52.127394</td>\n", "      <td>6.758793</td>\n", "      <td>127.175293</td>\n", "      <td>coffee</td>\n", "    </tr>\n", "    <tr>\n", "      <th>899</th>\n", "      <td>23.603016</td>\n", "      <td>60.396475</td>\n", "      <td>6.779833</td>\n", "      <td>140.937041</td>\n", "      <td>coffee</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2200 rows × 5 columns</p>\n", "</div>"], "text/plain": ["     temperature   humidity        ph    rainfall   label\n", "0      20.879744  82.002744  6.502985  202.935536    rice\n", "1      21.770462  80.319644  7.038096  226.655537    rice\n", "2      23.004459  82.320763  7.840207  263.964248    rice\n", "3      26.491096  80.158363  6.980401  242.864034    rice\n", "4      20.130175  81.604873  7.628473  262.717340    rice\n", "..           ...        ...       ...         ...     ...\n", "895    26.774637  66.413269  6.780064  177.774507  coffee\n", "896    27.417112  56.636362  6.086922  127.924610  coffee\n", "897    24.131797  67.225123  6.362608  173.322839  coffee\n", "898    26.272418  52.127394  6.758793  127.175293  coffee\n", "899    23.603016  60.396475  6.779833  140.937041  coffee\n", "\n", "[2200 rows x 5 columns]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["new_crop"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Crop</th>\n", "      <th>N</th>\n", "      <th>P</th>\n", "      <th>K</th>\n", "      <th>pH</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>rice</td>\n", "      <td>80</td>\n", "      <td>40</td>\n", "      <td>40</td>\n", "      <td>5.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>maize</td>\n", "      <td>80</td>\n", "      <td>40</td>\n", "      <td>20</td>\n", "      <td>5.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>chickpea</td>\n", "      <td>40</td>\n", "      <td>60</td>\n", "      <td>80</td>\n", "      <td>5.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>kidneybeans</td>\n", "      <td>20</td>\n", "      <td>60</td>\n", "      <td>20</td>\n", "      <td>5.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>pigeonpeas</td>\n", "      <td>20</td>\n", "      <td>60</td>\n", "      <td>20</td>\n", "      <td>5.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>mothbeans</td>\n", "      <td>20</td>\n", "      <td>40</td>\n", "      <td>20</td>\n", "      <td>5.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>mungbean</td>\n", "      <td>20</td>\n", "      <td>40</td>\n", "      <td>20</td>\n", "      <td>5.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>blackgram</td>\n", "      <td>40</td>\n", "      <td>60</td>\n", "      <td>20</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>lentil</td>\n", "      <td>20</td>\n", "      <td>60</td>\n", "      <td>20</td>\n", "      <td>5.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>60</th>\n", "      <td>pomegranate</td>\n", "      <td>20</td>\n", "      <td>10</td>\n", "      <td>40</td>\n", "      <td>5.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61</th>\n", "      <td>banana</td>\n", "      <td>100</td>\n", "      <td>75</td>\n", "      <td>50</td>\n", "      <td>6.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>62</th>\n", "      <td>mango</td>\n", "      <td>20</td>\n", "      <td>20</td>\n", "      <td>30</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63</th>\n", "      <td>grapes</td>\n", "      <td>20</td>\n", "      <td>125</td>\n", "      <td>200</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66</th>\n", "      <td>watermelon</td>\n", "      <td>100</td>\n", "      <td>10</td>\n", "      <td>50</td>\n", "      <td>5.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67</th>\n", "      <td>muskmelon</td>\n", "      <td>100</td>\n", "      <td>10</td>\n", "      <td>50</td>\n", "      <td>5.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>69</th>\n", "      <td>apple</td>\n", "      <td>20</td>\n", "      <td>125</td>\n", "      <td>200</td>\n", "      <td>6.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>74</th>\n", "      <td>orange</td>\n", "      <td>20</td>\n", "      <td>10</td>\n", "      <td>10</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75</th>\n", "      <td>papaya</td>\n", "      <td>50</td>\n", "      <td>50</td>\n", "      <td>50</td>\n", "      <td>6.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>88</th>\n", "      <td>coconut</td>\n", "      <td>20</td>\n", "      <td>10</td>\n", "      <td>30</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93</th>\n", "      <td>cotton</td>\n", "      <td>120</td>\n", "      <td>40</td>\n", "      <td>20</td>\n", "      <td>5.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>94</th>\n", "      <td>jute</td>\n", "      <td>80</td>\n", "      <td>40</td>\n", "      <td>40</td>\n", "      <td>5.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>95</th>\n", "      <td>coffee</td>\n", "      <td>100</td>\n", "      <td>20</td>\n", "      <td>30</td>\n", "      <td>5.5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           Crop    N    P    K   pH\n", "0          rice   80   40   40  5.5\n", "3         maize   80   40   20  5.5\n", "5      chickpea   40   60   80  5.5\n", "12  kidneybeans   20   60   20  5.5\n", "13   pigeonpeas   20   60   20  5.5\n", "14    mothbeans   20   40   20  5.5\n", "15     mungbean   20   40   20  5.5\n", "18    blackgram   40   60   20  5.0\n", "24       lentil   20   60   20  5.5\n", "60  pomegranate   20   10   40  5.5\n", "61       banana  100   75   50  6.5\n", "62        mango   20   20   30  5.0\n", "63       grapes   20  125  200  4.0\n", "66   watermelon  100   10   50  5.5\n", "67    muskmelon  100   10   50  5.5\n", "69        apple   20  125  200  6.5\n", "74       orange   20   10   10  4.0\n", "75       papaya   50   50   50  6.0\n", "88      coconut   20   10   30  5.0\n", "93       cotton  120   40   20  5.5\n", "94         jute   80   40   40  5.5\n", "95       coffee  100   20   30  5.5"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["new_fert"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["new_crop.to_csv('../Data-raw/MergeFileCrop.csv')\n", "new_fert.to_csv('../Data-raw/FertilizerData.csv')"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}