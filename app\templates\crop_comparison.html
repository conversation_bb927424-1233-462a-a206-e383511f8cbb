{% extends 'layout.html' %}

{% block body %}
<!-- Page Header -->
<div class="page-header">
	<div class="container">
		<h1 data-aos="fade-up">
			<i class="fas fa-chart-line me-3"></i>Crop Comparison Dashboard
		</h1>
		<p data-aos="fade-up" data-aos-delay="200">
			Compare crops with detailed profit analysis, ROI calculations, and comprehensive agricultural data to make informed farming decisions
		</p>
	</div>
</div>

<!-- Main Content -->
<div class="container py-5">
	<div class="row">
		<!-- Sidebar - Crop Selection -->
		<div class="col-lg-3">
			<div class="card-modern sticky-top" style="top: 100px;">
				<div class="p-4">
					<h5 class="fw-bold mb-3">
						<i class="fas fa-seedling me-2 text-success"></i>Select Crops to Compare
					</h5>
					
					<!-- Crop Categories -->
					<div class="crop-categories mb-4">
						<div class="category-section mb-3">
							<h6 class="fw-semibold text-primary mb-2">Cereals</h6>
							<div class="form-check">
								<input class="form-check-input" type="checkbox" value="rice" id="crop-rice">
								<label class="form-check-label" for="crop-rice">Rice</label>
							</div>
							<div class="form-check">
								<input class="form-check-input" type="checkbox" value="wheat" id="crop-wheat">
								<label class="form-check-label" for="crop-wheat">Wheat</label>
							</div>
							<div class="form-check">
								<input class="form-check-input" type="checkbox" value="maize" id="crop-maize">
								<label class="form-check-label" for="crop-maize">Maize</label>
							</div>
						</div>
						
						<div class="category-section mb-3">
							<h6 class="fw-semibold text-warning mb-2">Cash Crops</h6>
							<div class="form-check">
								<input class="form-check-input" type="checkbox" value="sugarcane" id="crop-sugarcane">
								<label class="form-check-label" for="crop-sugarcane">Sugarcane</label>
							</div>
							<div class="form-check">
								<input class="form-check-input" type="checkbox" value="cotton" id="crop-cotton">
								<label class="form-check-label" for="crop-cotton">Cotton</label>
							</div>
						</div>
						
						<div class="category-section mb-3">
							<h6 class="fw-semibold text-success mb-2">Vegetables</h6>
							<div class="form-check">
								<input class="form-check-input" type="checkbox" value="tomato" id="crop-tomato">
								<label class="form-check-label" for="crop-tomato">Tomato</label>
							</div>
							<div class="form-check">
								<input class="form-check-input" type="checkbox" value="potato" id="crop-potato">
								<label class="form-check-label" for="crop-potato">Potato</label>
							</div>
							<div class="form-check">
								<input class="form-check-input" type="checkbox" value="onion" id="crop-onion">
								<label class="form-check-label" for="crop-onion">Onion</label>
							</div>
						</div>
						
						<div class="category-section mb-3">
							<h6 class="fw-semibold text-info mb-2">Fruits</h6>
							<div class="form-check">
								<input class="form-check-input" type="checkbox" value="banana" id="crop-banana">
								<label class="form-check-label" for="crop-banana">Banana</label>
							</div>
							<div class="form-check">
								<input class="form-check-input" type="checkbox" value="mango" id="crop-mango">
								<label class="form-check-label" for="crop-mango">Mango</label>
							</div>
						</div>
					</div>
					
					<button class="btn btn-modern w-100" onclick="compareCrops()">
						<i class="fas fa-chart-bar me-2"></i>Compare Selected Crops
					</button>
					
					<button class="btn btn-outline-secondary w-100 mt-2" onclick="clearSelection()">
						<i class="fas fa-times me-2"></i>Clear Selection
					</button>
				</div>
			</div>
		</div>
		
		<!-- Main Content Area -->
		<div class="col-lg-9">
			<!-- Loading Indicator -->
			<div class="text-center mb-4" id="loadingIndicator" style="display: none;">
				<div class="spinner-border text-primary" role="status">
					<span class="visually-hidden">Loading...</span>
				</div>
				<p class="mt-2 text-muted">Analyzing crop data and generating comparisons...</p>
			</div>
			
			<!-- Welcome Message -->
			<div class="card-modern text-center p-5" id="welcomeMessage">
				<i class="fas fa-chart-line fa-4x text-primary mb-3"></i>
				<h3 class="fw-bold mb-3">Welcome to Crop Comparison Dashboard</h3>
				<p class="text-muted mb-4">
					Select crops from the sidebar to compare their profitability, resource requirements, 
					and market potential. Get detailed insights to make informed farming decisions.
				</p>
				<div class="row text-center">
					<div class="col-md-3">
						<i class="fas fa-dollar-sign fa-2x text-success mb-2"></i>
						<h6>Profit Analysis</h6>
					</div>
					<div class="col-md-3">
						<i class="fas fa-percentage fa-2x text-warning mb-2"></i>
						<h6>ROI Calculation</h6>
					</div>
					<div class="col-md-3">
						<i class="fas fa-tint fa-2x text-info mb-2"></i>
						<h6>Resource Requirements</h6>
					</div>
					<div class="col-md-3">
						<i class="fas fa-clock fa-2x text-primary mb-2"></i>
						<h6>Growing Period</h6>
					</div>
				</div>
			</div>
			
			<!-- Comparison Results -->
			<div id="comparisonResults" style="display: none;">
				<!-- Summary Cards -->
				<div class="row mb-4" id="summaryCards">
					<!-- Summary cards will be populated here -->
				</div>
				
				<!-- Charts Section -->
				<div class="row mb-4">
					<div class="col-lg-6 mb-4">
						<div class="card-modern p-4">
							<h5 class="fw-bold mb-3">
								<i class="fas fa-chart-bar me-2 text-success"></i>Profit Comparison
							</h5>
							<canvas id="profitChart" width="400" height="300"></canvas>
						</div>
					</div>
					<div class="col-lg-6 mb-4">
						<div class="card-modern p-4">
							<h5 class="fw-bold mb-3">
								<i class="fas fa-percentage me-2 text-warning"></i>ROI Comparison
							</h5>
							<canvas id="roiChart" width="400" height="300"></canvas>
						</div>
					</div>
				</div>
				
				<div class="row mb-4">
					<div class="col-lg-6 mb-4">
						<div class="card-modern p-4">
							<h5 class="fw-bold mb-3">
								<i class="fas fa-tint me-2 text-info"></i>Water Requirements
							</h5>
							<canvas id="waterChart" width="400" height="300"></canvas>
						</div>
					</div>
					<div class="col-lg-6 mb-4">
						<div class="card-modern p-4">
							<h5 class="fw-bold mb-3">
								<i class="fas fa-users me-2 text-primary"></i>Labor Requirements
							</h5>
							<canvas id="laborChart" width="400" height="300"></canvas>
						</div>
					</div>
				</div>
				
				<!-- Detailed Comparison Table -->
				<div class="card-modern p-4 mb-4">
					<h5 class="fw-bold mb-3">
						<i class="fas fa-table me-2 text-secondary"></i>Detailed Comparison
					</h5>
					<div class="table-responsive">
						<table class="table table-hover" id="comparisonTable">
							<thead class="table-dark">
								<tr>
									<th>Crop</th>
									<th>Category</th>
									<th>Growing Period</th>
									<th>Total Cost</th>
									<th>Revenue</th>
									<th>Profit</th>
									<th>ROI (%)</th>
									<th>Water (mm)</th>
									<th>Labor (days)</th>
									<th>Market Demand</th>
								</tr>
							</thead>
							<tbody id="comparisonTableBody">
								<!-- Table rows will be populated here -->
							</tbody>
						</table>
					</div>
				</div>
				
				<!-- Detailed Crop Information -->
				<div id="detailedCropInfo">
					<!-- Detailed crop cards will be populated here -->
				</div>
			</div>
			
			<!-- Error Message -->
			<div class="alert alert-danger" id="errorMessage" style="display: none;">
				<i class="fas fa-exclamation-triangle me-2"></i>
				<span id="errorText"></span>
			</div>
		</div>
	</div>
</div>

<!-- Chart.js Library -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<style>
.sticky-top {
	position: sticky;
	top: 100px;
	z-index: 1020;
}

.category-section {
	border-left: 3px solid #e9ecef;
	padding-left: 1rem;
}

.form-check {
	margin-bottom: 0.5rem;
}

.form-check-input:checked {
	background-color: var(--primary-green);
	border-color: var(--primary-green);
}

.comparison-card {
	background: white;
	border-radius: 15px;
	padding: 1.5rem;
	box-shadow: 0 4px 6px rgba(0,0,0,0.1);
	border-left: 4px solid #007bff;
	transition: all 0.3s ease;
}

.comparison-card:hover {
	transform: translateY(-5px);
	box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.profit-positive {
	color: #28a745;
	font-weight: bold;
}

.profit-negative {
	color: #dc3545;
	font-weight: bold;
}

.roi-excellent {
	background: linear-gradient(135deg, #28a745, #20c997);
	color: white;
}

.roi-good {
	background: linear-gradient(135deg, #ffc107, #fd7e14);
	color: white;
}

.roi-poor {
	background: linear-gradient(135deg, #dc3545, #e83e8c);
	color: white;
}

.crop-detail-card {
	background: white;
	border-radius: 15px;
	padding: 2rem;
	margin-bottom: 2rem;
	box-shadow: 0 4px 6px rgba(0,0,0,0.1);
	border-top: 4px solid #007bff;
}

.metric-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0.75rem 0;
	border-bottom: 1px solid #f8f9fa;
}

.metric-item:last-child {
	border-bottom: none;
}

.metric-label {
	font-weight: 600;
	color: #495057;
}

.metric-value {
	font-weight: bold;
	color: #007bff;
}

.chart-container {
	position: relative;
	height: 300px;
	margin: 1rem 0;
}
</style>

<script>
let selectedCrops = [];
let cropData = {};
let charts = {};

// Load crop data on page load
document.addEventListener('DOMContentLoaded', function() {
	loadCropData();
});

function loadCropData() {
	fetch('/crop-data-api')
	.then(response => response.json())
	.then(data => {
		if (data.success) {
			cropData = data;
		} else {
			showError('Failed to load crop data');
		}
	})
	.catch(error => {
		showError('Network error while loading crop data');
		console.error('Error:', error);
	});
}

function compareCrops() {
	// Get selected crops
	selectedCrops = [];
	document.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
		selectedCrops.push(checkbox.value);
	});
	
	if (selectedCrops.length === 0) {
		showError('Please select at least one crop to compare');
		return;
	}
	
	// Show loading
	document.getElementById('loadingIndicator').style.display = 'block';
	document.getElementById('welcomeMessage').style.display = 'none';
	document.getElementById('comparisonResults').style.display = 'none';
	document.getElementById('errorMessage').style.display = 'none';
	
	// Fetch comparison data
	fetch('/crop-compare-api', {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
		},
		body: JSON.stringify({ crops: selectedCrops })
	})
	.then(response => response.json())
	.then(data => {
		document.getElementById('loadingIndicator').style.display = 'none';
		
		if (data.success) {
			displayComparisonResults(data);
		} else {
			showError(data.error || 'Failed to compare crops');
		}
	})
	.catch(error => {
		document.getElementById('loadingIndicator').style.display = 'none';
		showError('Network error during comparison');
		console.error('Error:', error);
	});
}

function clearSelection() {
	document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
		checkbox.checked = false;
	});
	selectedCrops = [];
	document.getElementById('welcomeMessage').style.display = 'block';
	document.getElementById('comparisonResults').style.display = 'none';
	document.getElementById('errorMessage').style.display = 'none';
	
	// Destroy existing charts
	Object.values(charts).forEach(chart => {
		if (chart) chart.destroy();
	});
	charts = {};
}

function displayComparisonResults(data) {
	const comparison = data.comparison;
	
	// Display summary cards
	displaySummaryCards(comparison);
	
	// Create charts
	createProfitChart(comparison);
	createROIChart(comparison);
	createWaterChart(comparison);
	createLaborChart(comparison);
	
	// Display comparison table
	displayComparisonTable(comparison);
	
	// Display detailed crop information
	displayDetailedCropInfo(comparison);
	
	// Show results
	document.getElementById('comparisonResults').style.display = 'block';
}

function displaySummaryCards(comparison) {
	const summaryCards = document.getElementById('summaryCards');
	summaryCards.innerHTML = '';
	
	// Calculate summary statistics
	const crops = Object.values(comparison);
	const totalCrops = crops.length;
	const avgROI = crops.reduce((sum, crop) => sum + crop.roi, 0) / totalCrops;
	const maxProfit = Math.max(...crops.map(crop => crop.profit));
	const bestROICrop = crops.reduce((best, crop) => crop.roi > best.roi ? crop : best);
	
	const summaryData = [
		{
			title: 'Crops Compared',
			value: totalCrops,
			icon: 'fas fa-seedling',
			color: 'primary'
		},
		{
			title: 'Average ROI',
			value: avgROI.toFixed(1) + '%',
			icon: 'fas fa-percentage',
			color: 'success'
		},
		{
			title: 'Max Profit',
			value: '₹' + maxProfit.toLocaleString(),
			icon: 'fas fa-rupee-sign',
			color: 'warning'
		},
		{
			title: 'Best ROI Crop',
			value: bestROICrop.name,
			icon: 'fas fa-trophy',
			color: 'info'
		}
	];
	
	summaryData.forEach(item => {
		const card = document.createElement('div');
		card.className = 'col-lg-3 col-md-6 mb-3';
		card.innerHTML = `
			<div class="comparison-card text-center">
				<i class="${item.icon} fa-2x text-${item.color} mb-2"></i>
				<h6 class="fw-bold">${item.title}</h6>
				<h4 class="text-${item.color} mb-0">${item.value}</h4>
			</div>
		`;
		summaryCards.appendChild(card);
	});
}

function createProfitChart(comparison) {
	const ctx = document.getElementById('profitChart').getContext('2d');
	if (charts.profit) charts.profit.destroy();

	const labels = Object.values(comparison).map(crop => crop.name);
	const profits = Object.values(comparison).map(crop => crop.profit);

	charts.profit = new Chart(ctx, {
		type: 'bar',
		data: {
			labels: labels,
			datasets: [{
				label: 'Profit (₹/hectare)',
				data: profits,
				backgroundColor: profits.map(profit => profit >= 0 ? '#28a745' : '#dc3545'),
				borderColor: profits.map(profit => profit >= 0 ? '#1e7e34' : '#c82333'),
				borderWidth: 2
			}]
		},
		options: {
			responsive: true,
			maintainAspectRatio: false,
			scales: {
				y: {
					beginAtZero: true,
					ticks: {
						callback: function(value) {
							return '₹' + value.toLocaleString();
						}
					}
				}
			},
			plugins: {
				tooltip: {
					callbacks: {
						label: function(context) {
							return 'Profit: ₹' + context.parsed.y.toLocaleString();
						}
					}
				}
			}
		}
	});
}

function createROIChart(comparison) {
	const ctx = document.getElementById('roiChart').getContext('2d');
	if (charts.roi) charts.roi.destroy();

	const labels = Object.values(comparison).map(crop => crop.name);
	const rois = Object.values(comparison).map(crop => crop.roi);

	charts.roi = new Chart(ctx, {
		type: 'doughnut',
		data: {
			labels: labels,
			datasets: [{
				label: 'ROI (%)',
				data: rois.map(roi => Math.abs(roi)),
				backgroundColor: [
					'#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
					'#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
				],
				borderWidth: 2
			}]
		},
		options: {
			responsive: true,
			maintainAspectRatio: false,
			plugins: {
				tooltip: {
					callbacks: {
						label: function(context) {
							const originalROI = rois[context.dataIndex];
							return context.label + ': ' + originalROI.toFixed(1) + '%';
						}
					}
				}
			}
		}
	});
}

function createWaterChart(comparison) {
	const ctx = document.getElementById('waterChart').getContext('2d');
	if (charts.water) charts.water.destroy();

	const labels = Object.values(comparison).map(crop => crop.name);
	const water = Object.values(comparison).map(crop => crop.water_requirement);

	charts.water = new Chart(ctx, {
		type: 'line',
		data: {
			labels: labels,
			datasets: [{
				label: 'Water Requirement (mm)',
				data: water,
				borderColor: '#17a2b8',
				backgroundColor: 'rgba(23, 162, 184, 0.1)',
				borderWidth: 3,
				fill: true,
				tension: 0.4
			}]
		},
		options: {
			responsive: true,
			maintainAspectRatio: false,
			scales: {
				y: {
					beginAtZero: true,
					ticks: {
						callback: function(value) {
							return value + ' mm';
						}
					}
				}
			}
		}
	});
}

function createLaborChart(comparison) {
	const ctx = document.getElementById('laborChart').getContext('2d');
	if (charts.labor) charts.labor.destroy();

	const labels = Object.values(comparison).map(crop => crop.name);
	const labor = Object.values(comparison).map(crop => crop.labor_requirement);

	charts.labor = new Chart(ctx, {
		type: 'radar',
		data: {
			labels: labels,
			datasets: [{
				label: 'Labor Requirement (person-days)',
				data: labor,
				borderColor: '#6f42c1',
				backgroundColor: 'rgba(111, 66, 193, 0.2)',
				borderWidth: 2,
				pointBackgroundColor: '#6f42c1'
			}]
		},
		options: {
			responsive: true,
			maintainAspectRatio: false,
			scales: {
				r: {
					beginAtZero: true,
					ticks: {
						callback: function(value) {
							return value + ' days';
						}
					}
				}
			}
		}
	});
}

function displayComparisonTable(comparison) {
	const tableBody = document.getElementById('comparisonTableBody');
	tableBody.innerHTML = '';

	Object.entries(comparison).forEach(([cropKey, crop]) => {
		const row = document.createElement('tr');
		row.innerHTML = `
			<td class="fw-bold">${crop.name}</td>
			<td><span class="badge bg-secondary">${crop.category}</span></td>
			<td>${crop.growing_period} days</td>
			<td>₹${crop.total_cost.toLocaleString()}</td>
			<td>₹${crop.total_revenue.toLocaleString()}</td>
			<td class="${crop.profit >= 0 ? 'profit-positive' : 'profit-negative'}">
				₹${crop.profit.toLocaleString()}
			</td>
			<td class="${crop.roi >= 20 ? 'profit-positive' : crop.roi >= 0 ? 'text-warning' : 'profit-negative'}">
				${crop.roi.toFixed(1)}%
			</td>
			<td>${crop.water_requirement} mm</td>
			<td>${crop.labor_requirement} days</td>
			<td><span class="badge ${crop.market_demand === 'Very High' ? 'bg-success' : 'bg-warning'}">${crop.market_demand}</span></td>
		`;
		tableBody.appendChild(row);
	});
}

function displayDetailedCropInfo(comparison) {
	const detailedInfo = document.getElementById('detailedCropInfo');
	detailedInfo.innerHTML = '<h5 class="fw-bold mb-4"><i class="fas fa-info-circle me-2"></i>Detailed Crop Information</h5>';

	Object.entries(comparison).forEach(([cropKey, crop]) => {
		const cropCard = document.createElement('div');
		cropCard.className = 'crop-detail-card';
		cropCard.innerHTML = `
			<div class="row">
				<div class="col-md-6">
					<h4 class="fw-bold text-primary mb-3">${crop.name}</h4>

					<h6 class="fw-bold mb-2">Financial Analysis</h6>
					<div class="metric-item">
						<span class="metric-label">Total Investment</span>
						<span class="metric-value">₹${crop.total_cost.toLocaleString()}</span>
					</div>
					<div class="metric-item">
						<span class="metric-label">Expected Revenue</span>
						<span class="metric-value">₹${crop.total_revenue.toLocaleString()}</span>
					</div>
					<div class="metric-item">
						<span class="metric-label">Net Profit</span>
						<span class="metric-value ${crop.profit >= 0 ? 'profit-positive' : 'profit-negative'}">
							₹${crop.profit.toLocaleString()}
						</span>
					</div>
					<div class="metric-item">
						<span class="metric-label">Return on Investment</span>
						<span class="metric-value ${crop.roi >= 0 ? 'profit-positive' : 'profit-negative'}">
							${crop.roi.toFixed(1)}%
						</span>
					</div>

					<h6 class="fw-bold mb-2 mt-3">Production Details</h6>
					<div class="metric-item">
						<span class="metric-label">Average Yield</span>
						<span class="metric-value">${crop.average_yield.toLocaleString()} kg/ha</span>
					</div>
					<div class="metric-item">
						<span class="metric-label">Market Price</span>
						<span class="metric-value">₹${crop.market_price}/kg</span>
					</div>
					<div class="metric-item">
						<span class="metric-label">Storage Life</span>
						<span class="metric-value">${crop.storage_life} months</span>
					</div>
				</div>

				<div class="col-md-6">
					<h6 class="fw-bold mb-2">Resource Requirements</h6>
					<div class="metric-item">
						<span class="metric-label">Growing Period</span>
						<span class="metric-value">${crop.growing_period} days</span>
					</div>
					<div class="metric-item">
						<span class="metric-label">Water Requirement</span>
						<span class="metric-value">${crop.water_requirement} mm</span>
					</div>
					<div class="metric-item">
						<span class="metric-label">Labor Requirement</span>
						<span class="metric-value">${crop.labor_requirement} person-days</span>
					</div>

					<h6 class="fw-bold mb-2 mt-3">Growing Conditions</h6>
					<div class="metric-item">
						<span class="metric-label">Soil Type</span>
						<span class="metric-value">${crop.soil_type.join(', ')}</span>
					</div>
					<div class="metric-item">
						<span class="metric-label">Climate</span>
						<span class="metric-value">${crop.climate}</span>
					</div>
					<div class="metric-item">
						<span class="metric-label">Season</span>
						<span class="metric-value">${crop.season}</span>
					</div>
					<div class="metric-item">
						<span class="metric-label">pH Range</span>
						<span class="metric-value">${crop.ph_range[0]} - ${crop.ph_range[1]}</span>
					</div>
					<div class="metric-item">
						<span class="metric-label">Temperature Range</span>
						<span class="metric-value">${crop.temperature_range[0]}°C - ${crop.temperature_range[1]}°C</span>
					</div>

					<h6 class="fw-bold mb-2 mt-3">Market Information</h6>
					<div class="metric-item">
						<span class="metric-label">Market Demand</span>
						<span class="badge ${crop.market_demand === 'Very High' ? 'bg-success' : 'bg-warning'}">${crop.market_demand}</span>
					</div>
					<div class="metric-item">
						<span class="metric-label">Processing Options</span>
						<span class="metric-value">${crop.processing_options.join(', ')}</span>
					</div>
				</div>
			</div>
		`;
		detailedInfo.appendChild(cropCard);
	});
}

function showError(message) {
	document.getElementById('errorText').textContent = message;
	document.getElementById('errorMessage').style.display = 'block';
}
</script>

{% endblock %}
