{% extends 'layout.html' %}

{% block body %}
<!-- <PERSON> Header -->
<div class="page-header">
	<div class="container">
		<h1 data-aos="fade-up">
			<i class="fas fa-wheat-awn me-3"></i>Smart Crop Recommendation
		</h1>
		<p data-aos="fade-up" data-aos-delay="200">
			Get AI-powered crop suggestions based on your soil conditions, climate data, and local weather patterns
		</p>
	</div>
</div>

<!-- Main Content -->
<div class="container py-5">
	<div class="row justify-content-center">
		<div class="col-lg-8">
			<!-- Info Cards -->
			<div class="row mb-5">
				<div class="col-md-4 mb-3" data-aos="fade-up" data-aos-delay="100">
					<div class="card-modern text-center p-3">
						<i class="fas fa-seedling text-success fa-2x mb-2"></i>
						<h6 class="fw-bold">AI Analysis</h6>
						<small class="text-muted">Advanced machine learning algorithms</small>
					</div>
				</div>
				<div class="col-md-4 mb-3" data-aos="fade-up" data-aos-delay="200">
					<div class="card-modern text-center p-3">
						<i class="fas fa-cloud-sun text-warning fa-2x mb-2"></i>
						<h6 class="fw-bold">Weather Data</h6>
						<small class="text-muted">Real-time climate integration</small>
					</div>
				</div>
				<div class="col-md-4 mb-3" data-aos="fade-up" data-aos-delay="300">
					<div class="card-modern text-center p-3">
						<i class="fas fa-chart-line text-info fa-2x mb-2"></i>
						<h6 class="fw-bold">95% Accuracy</h6>
						<small class="text-muted">Proven prediction results</small>
					</div>
				</div>
			</div>

			<!-- Form -->
			<div class="form-modern" data-aos="fade-up" data-aos-delay="400">
				<div class="text-center mb-4">
					<h3 class="text-gradient fw-bold">Enter Your Soil Parameters</h3>
					<p class="text-muted">Provide accurate soil and location data for the best recommendations</p>
				</div>

				<form method="POST" action="{{ url_for('crop_prediction') }}">
					<div class="row">
						<!-- Soil Nutrients -->
						<div class="col-md-6 mb-3">
							<label for="Nitrogen" class="form-label-modern">
								<i class="fas fa-flask me-2 text-primary"></i>Nitrogen (N)
							</label>
							<input type="number" class="form-control form-control-modern" id="Nitrogen"
								   name="nitrogen" placeholder="Enter nitrogen content (e.g., 50)" required>
							<small class="text-muted">Nitrogen content in kg/ha</small>
						</div>

						<div class="col-md-6 mb-3">
							<label for="Phosphorous" class="form-label-modern">
								<i class="fas fa-flask me-2 text-warning"></i>Phosphorous (P)
							</label>
							<input type="number" class="form-control form-control-modern" id="Phosphorous"
								   name="phosphorous" placeholder="Enter phosphorous content (e.g., 50)" required>
							<small class="text-muted">Phosphorous content in kg/ha</small>
						</div>

						<div class="col-md-6 mb-3">
							<label for="Pottasium" class="form-label-modern">
								<i class="fas fa-flask me-2 text-success"></i>Potassium (K)
							</label>
							<input type="number" class="form-control form-control-modern" id="Pottasium"
								   name="pottasium" placeholder="Enter potassium content (e.g., 50)" required>
							<small class="text-muted">Potassium content in kg/ha</small>
						</div>

						<div class="col-md-6 mb-3">
							<label for="ph" class="form-label-modern">
								<i class="fas fa-vial me-2 text-info"></i>pH Level
							</label>
							<input type="number" step="0.01" class="form-control form-control-modern" id="ph"
								   name="ph" placeholder="Enter pH value (e.g., 6.5)" required>
							<small class="text-muted">Soil pH level (0-14 scale)</small>
						</div>

						<div class="col-md-6 mb-3">
							<label for="Rainfall" class="form-label-modern">
								<i class="fas fa-cloud-rain me-2 text-primary"></i>Rainfall
							</label>
							<input type="number" step="0.01" class="form-control form-control-modern" id="Rainfall"
								   name="rainfall" placeholder="Enter rainfall (e.g., 200)" required>
							<small class="text-muted">Annual rainfall in mm</small>
						</div>

						<!-- Location -->
						<div class="col-md-6 mb-3">
							<label for="State" class="form-label-modern">
								<i class="fas fa-map me-2 text-success"></i>State
							</label>
							<select onchange="print_city('state', this.selectedIndex);" id="sts" name="stt"
									class="form-control form-control-modern" required>
								<option value="">Select your state</option>
							</select>
						</div>

						<div class="col-md-12 mb-4">
							<label for="City" class="form-label-modern">
								<i class="fas fa-map-marker-alt me-2 text-warning"></i>City
							</label>
							<select id="state" class="form-control form-control-modern" name="city" required>
								<option value="">Select your city</option>
							</select>
							<script language="javascript">
								print_state("sts");
							</script>
						</div>
					</div>

					<!-- Submit Button -->
					<div class="text-center">
						<button type="submit" class="btn btn-modern btn-lg px-5">
							<i class="fas fa-brain me-2"></i>Get AI Recommendation
						</button>
					</div>
				</form>
			</div>

			<!-- How it Works -->
			<div class="mt-5" data-aos="fade-up">
				<h4 class="text-center mb-4 fw-bold">How Our AI Works</h4>
				<div class="row">
					<div class="col-md-4 text-center mb-3">
						<div class="bg-primary bg-opacity-10 rounded-circle p-3 d-inline-block mb-3">
							<i class="fas fa-database text-primary fa-2x"></i>
						</div>
						<h6 class="fw-bold">Data Analysis</h6>
						<small class="text-muted">Analyzes soil nutrients, pH, and rainfall data</small>
					</div>
					<div class="col-md-4 text-center mb-3">
						<div class="bg-warning bg-opacity-10 rounded-circle p-3 d-inline-block mb-3">
							<i class="fas fa-cloud text-warning fa-2x"></i>
						</div>
						<h6 class="fw-bold">Weather Integration</h6>
						<small class="text-muted">Fetches real-time weather data for your location</small>
					</div>
					<div class="col-md-4 text-center mb-3">
						<div class="bg-success bg-opacity-10 rounded-circle p-3 d-inline-block mb-3">
							<i class="fas fa-magic text-success fa-2x"></i>
						</div>
						<h6 class="fw-bold">AI Prediction</h6>
						<small class="text-muted">Recommends the best crop for maximum yield</small>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

{% endblock %}
