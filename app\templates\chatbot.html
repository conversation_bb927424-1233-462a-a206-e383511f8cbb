{% extends 'layout.html' %}

{% block body %}
<!-- <PERSON> Header -->
<div class="page-header">
	<div class="container">
		<h1 data-aos="fade-up">
			<i class="fas fa-robot me-3"></i>AI Agriculture Assistant
		</h1>
		<p data-aos="fade-up" data-aos-delay="200">
			Get instant expert advice on farming, crops, soil management, and agricultural best practices from our AI-powered assistant
		</p>
	</div>
</div>

<!-- Main Content -->
<div class="container py-5">
	<div class="row justify-content-center">
		<div class="col-lg-8">
			<!-- Info Cards -->
			<div class="row mb-5">
				<div class="col-md-4 mb-3" data-aos="fade-up" data-aos-delay="100">
					<div class="card-modern text-center p-3">
						<i class="fas fa-brain text-primary fa-2x mb-2"></i>
						<h6 class="fw-bold">AI Powered</h6>
						<small class="text-muted">Advanced agricultural knowledge base</small>
					</div>
				</div>
				<div class="col-md-4 mb-3" data-aos="fade-up" data-aos-delay="200">
					<div class="card-modern text-center p-3">
						<i class="fas fa-clock text-success fa-2x mb-2"></i>
						<h6 class="fw-bold">24/7 Available</h6>
						<small class="text-muted">Get help anytime, anywhere</small>
					</div>
				</div>
				<div class="col-md-4 mb-3" data-aos="fade-up" data-aos-delay="300">
					<div class="card-modern text-center p-3">
						<i class="fas fa-leaf text-warning fa-2x mb-2"></i>
						<h6 class="fw-bold">Expert Advice</h6>
						<small class="text-muted">Professional agricultural guidance</small>
					</div>
				</div>
			</div>

			<!-- Chatbot Interface -->
			<div class="form-modern" data-aos="fade-up" data-aos-delay="400">
				<div class="text-center mb-4">
					<h3 class="text-gradient fw-bold">Chat with AGRI BOT</h3>
					<p class="text-muted">Ask me anything about farming, crops, soil, diseases, or agricultural practices!</p>
				</div>

				<!-- Chat Container -->
				<div class="chat-container mb-4" id="chatContainer">
					<div class="chat-messages" id="chatMessages">
						<!-- Welcome Message -->
						<div class="message bot-message">
							<div class="message-avatar">
								<i class="fas fa-robot"></i>
							</div>
							<div class="message-content">
								<div class="message-bubble">
									<p class="mb-1">👋 Hello! I'm AGRI BOT, your AI agriculture assistant.</p>
									<p class="mb-0">I can help you with farming techniques, crop management, soil health, plant diseases, and much more. What would you like to know?</p>
								</div>
								<small class="message-time">Just now</small>
							</div>
						</div>
					</div>
				</div>

				<!-- Chat Input -->
				<div class="chat-input-container">
					<div class="input-group">
						<input type="text" class="form-control form-control-modern" id="messageInput" 
							   placeholder="Ask me about farming, crops, soil, diseases..." 
							   onkeypress="handleKeyPress(event)">
						<button class="btn btn-modern" type="button" id="sendButton" onclick="sendMessage()">
							<i class="fas fa-paper-plane me-1"></i>Send
						</button>
					</div>
				</div>

				<!-- Typing Indicator -->
				<div class="typing-indicator" id="typingIndicator" style="display: none;">
					<div class="message bot-message">
						<div class="message-avatar">
							<i class="fas fa-robot"></i>
						</div>
						<div class="message-content">
							<div class="typing-dots">
								<span></span>
								<span></span>
								<span></span>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- Quick Questions -->
			<div class="mt-5" data-aos="fade-up">
				<h4 class="text-center mb-4 fw-bold">Quick Questions to Get Started</h4>
				<div class="row">
					<div class="col-md-6 mb-3">
						<button class="btn btn-outline-primary w-100 text-start quick-question" 
								onclick="askQuestion('What crops grow best in sandy soil?')">
							<i class="fas fa-seedling me-2"></i>What crops grow best in sandy soil?
						</button>
					</div>
					<div class="col-md-6 mb-3">
						<button class="btn btn-outline-success w-100 text-start quick-question" 
								onclick="askQuestion('How do I improve soil fertility naturally?')">
							<i class="fas fa-leaf me-2"></i>How do I improve soil fertility naturally?
						</button>
					</div>
					<div class="col-md-6 mb-3">
						<button class="btn btn-outline-warning w-100 text-start quick-question" 
								onclick="askQuestion('What are signs of nitrogen deficiency in plants?')">
							<i class="fas fa-exclamation-triangle me-2"></i>Signs of nitrogen deficiency in plants?
						</button>
					</div>
					<div class="col-md-6 mb-3">
						<button class="btn btn-outline-info w-100 text-start quick-question" 
								onclick="askQuestion('Best irrigation practices for water conservation?')">
							<i class="fas fa-tint me-2"></i>Best irrigation practices for water conservation?
						</button>
					</div>
				</div>
			</div>

			<!-- Features -->
			<div class="mt-5" data-aos="fade-up">
				<h4 class="text-center mb-4 fw-bold">What I Can Help You With</h4>
				<div class="row">
					<div class="col-md-6 mb-3">
						<div class="d-flex align-items-start">
							<div class="bg-success bg-opacity-10 rounded-circle p-2 me-3 mt-1">
								<i class="fas fa-seedling text-success"></i>
							</div>
							<div>
								<h6 class="fw-bold">Crop Cultivation</h6>
								<small class="text-muted">Planting, growing, harvesting techniques for various crops</small>
							</div>
						</div>
					</div>
					<div class="col-md-6 mb-3">
						<div class="d-flex align-items-start">
							<div class="bg-warning bg-opacity-10 rounded-circle p-2 me-3 mt-1">
								<i class="fas fa-flask text-warning"></i>
							</div>
							<div>
								<h6 class="fw-bold">Soil Management</h6>
								<small class="text-muted">Soil health, fertilization, pH management, composting</small>
							</div>
						</div>
					</div>
					<div class="col-md-6 mb-3">
						<div class="d-flex align-items-start">
							<div class="bg-danger bg-opacity-10 rounded-circle p-2 me-3 mt-1">
								<i class="fas fa-bug text-danger"></i>
							</div>
							<div>
								<h6 class="fw-bold">Pest & Disease Control</h6>
								<small class="text-muted">Identification and treatment of plant diseases and pests</small>
							</div>
						</div>
					</div>
					<div class="col-md-6 mb-3">
						<div class="d-flex align-items-start">
							<div class="bg-info bg-opacity-10 rounded-circle p-2 me-3 mt-1">
								<i class="fas fa-cloud-sun text-info"></i>
							</div>
							<div>
								<h6 class="fw-bold">Weather & Climate</h6>
								<small class="text-muted">Weather considerations, seasonal planning, climate adaptation</small>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<style>
.chat-container {
	background: #f8f9fa;
	border-radius: 15px;
	padding: 1rem;
	max-height: 500px;
	overflow-y: auto;
	border: 1px solid #e9ecef;
}

.chat-messages {
	display: flex;
	flex-direction: column;
	gap: 1rem;
}

.message {
	display: flex;
	align-items: flex-start;
	gap: 0.75rem;
}

.user-message {
	flex-direction: row-reverse;
}

.message-avatar {
	width: 40px;
	height: 40px;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 1.2rem;
	flex-shrink: 0;
}

.bot-message .message-avatar {
	background: var(--gradient-primary);
	color: white;
}

.user-message .message-avatar {
	background: var(--gradient-secondary);
	color: white;
}

.message-content {
	flex: 1;
	max-width: 80%;
}

.user-message .message-content {
	text-align: right;
}

.message-bubble {
	background: white;
	padding: 0.75rem 1rem;
	border-radius: 15px;
	box-shadow: 0 2px 4px rgba(0,0,0,0.1);
	margin-bottom: 0.25rem;
}

.user-message .message-bubble {
	background: var(--primary-green);
	color: white;
}

.message-time {
	color: #6c757d;
	font-size: 0.75rem;
}

.chat-input-container {
	border-top: 1px solid #e9ecef;
	padding-top: 1rem;
}

.typing-indicator {
	margin-top: 1rem;
}

.typing-dots {
	display: flex;
	gap: 4px;
	padding: 0.75rem 1rem;
	background: white;
	border-radius: 15px;
	box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.typing-dots span {
	width: 8px;
	height: 8px;
	border-radius: 50%;
	background: #6c757d;
	animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
	0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
	40% { transform: scale(1); opacity: 1; }
}

.quick-question {
	transition: all 0.3s ease;
}

.quick-question:hover {
	transform: translateY(-2px);
	box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Scrollbar styling */
.chat-container::-webkit-scrollbar {
	width: 6px;
}

.chat-container::-webkit-scrollbar-track {
	background: #f1f1f1;
	border-radius: 3px;
}

.chat-container::-webkit-scrollbar-thumb {
	background: var(--primary-green);
	border-radius: 3px;
}
</style>

<script>
function sendMessage() {
	const messageInput = document.getElementById('messageInput');
	const message = messageInput.value.trim();
	
	if (!message) return;
	
	// Add user message to chat
	addMessage(message, 'user');
	
	// Clear input
	messageInput.value = '';
	
	// Show typing indicator
	showTypingIndicator();
	
	// Send message to backend
	fetch('/chatbot-api', {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
		},
		body: JSON.stringify({ message: message })
	})
	.then(response => response.json())
	.then(data => {
		hideTypingIndicator();
		addMessage(data.response, 'bot');
	})
	.catch(error => {
		hideTypingIndicator();
		addMessage('Sorry, I encountered an error. Please try again.', 'bot');
		console.error('Error:', error);
	});
}

function addMessage(message, sender) {
	const chatMessages = document.getElementById('chatMessages');
	const messageDiv = document.createElement('div');
	const currentTime = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
	
	messageDiv.className = `message ${sender}-message`;
	messageDiv.innerHTML = `
		<div class="message-avatar">
			<i class="fas fa-${sender === 'user' ? 'user' : 'robot'}"></i>
		</div>
		<div class="message-content">
			<div class="message-bubble">
				<p class="mb-0">${message.replace(/\n/g, '<br>')}</p>
			</div>
			<small class="message-time">${currentTime}</small>
		</div>
	`;
	
	chatMessages.appendChild(messageDiv);
	
	// Scroll to bottom
	const chatContainer = document.getElementById('chatContainer');
	chatContainer.scrollTop = chatContainer.scrollHeight;
}

function showTypingIndicator() {
	document.getElementById('typingIndicator').style.display = 'block';
	const chatContainer = document.getElementById('chatContainer');
	chatContainer.scrollTop = chatContainer.scrollHeight;
}

function hideTypingIndicator() {
	document.getElementById('typingIndicator').style.display = 'none';
}

function handleKeyPress(event) {
	if (event.key === 'Enter') {
		sendMessage();
	}
}

function askQuestion(question) {
	document.getElementById('messageInput').value = question;
	sendMessage();
}

// Focus on input when page loads
document.addEventListener('DOMContentLoaded', function() {
	document.getElementById('messageInput').focus();
});
</script>

{% endblock %}
