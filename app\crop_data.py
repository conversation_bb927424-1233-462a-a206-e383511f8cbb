# Comprehensive crop data for comparison dashboard
# All prices are in INR (Indian Rupees) per hectare
# Data collected from various agricultural sources and market reports (2024)

CROP_DATA = {
    'rice': {
        'name': 'Rice',
        'category': 'Cereal',
        'growing_period': 120,  # days
        'water_requirement': 1500,  # mm per season
        'labor_requirement': 180,  # person-days per hectare
        'seed_cost': 8000,  # INR per hectare
        'fertilizer_cost': 15000,
        'pesticide_cost': 8000,
        'irrigation_cost': 12000,
        'labor_cost': 54000,  # 180 days * 300 INR/day
        'other_costs': 10000,
        'total_cost': 107000,
        'average_yield': 4500,  # kg per hectare
        'market_price': 25,  # INR per kg
        'total_revenue': 112500,
        'profit': 5500,
        'roi': 5.1,  # percentage
        'soil_type': ['Clay', 'Loamy'],
        'climate': 'Tropical, Subtropical',
        'season': 'Kharif',
        'ph_range': [5.5, 7.0],
        'temperature_range': [20, 35],
        'rainfall_requirement': [1000, 2000],
        'nutritional_value': {
            'protein': 7.1,
            'carbohydrates': 78.2,
            'fiber': 1.3,
            'calories_per_100g': 365
        },
        'market_demand': 'Very High',
        'storage_life': 12,  # months
        'processing_options': ['Milling', 'Parboiling', 'Flaking']
    },
    'wheat': {
        'name': 'Wheat',
        'category': 'Cereal',
        'growing_period': 120,
        'water_requirement': 450,
        'labor_requirement': 150,
        'seed_cost': 6000,
        'fertilizer_cost': 18000,
        'pesticide_cost': 7000,
        'irrigation_cost': 8000,
        'labor_cost': 45000,
        'other_costs': 8000,
        'total_cost': 92000,
        'average_yield': 3500,
        'market_price': 22,
        'total_revenue': 77000,
        'profit': -15000,
        'roi': -16.3,
        'soil_type': ['Loamy', 'Clay Loam'],
        'climate': 'Temperate, Semi-arid',
        'season': 'Rabi',
        'ph_range': [6.0, 7.5],
        'temperature_range': [10, 25],
        'rainfall_requirement': [300, 750],
        'nutritional_value': {
            'protein': 11.8,
            'carbohydrates': 71.2,
            'fiber': 12.2,
            'calories_per_100g': 327
        },
        'market_demand': 'High',
        'storage_life': 8,
        'processing_options': ['Flour Milling', 'Semolina', 'Biscuits']
    },
    'maize': {
        'name': 'Maize (Corn)',
        'category': 'Cereal',
        'growing_period': 90,
        'water_requirement': 500,
        'labor_requirement': 120,
        'seed_cost': 7000,
        'fertilizer_cost': 16000,
        'pesticide_cost': 6000,
        'irrigation_cost': 9000,
        'labor_cost': 36000,
        'other_costs': 7000,
        'total_cost': 81000,
        'average_yield': 5000,
        'market_price': 20,
        'total_revenue': 100000,
        'profit': 19000,
        'roi': 23.5,
        'soil_type': ['Loamy', 'Sandy Loam'],
        'climate': 'Tropical, Temperate',
        'season': 'Kharif',
        'ph_range': [5.8, 7.8],
        'temperature_range': [21, 27],
        'rainfall_requirement': [500, 750],
        'nutritional_value': {
            'protein': 9.4,
            'carbohydrates': 74.3,
            'fiber': 7.3,
            'calories_per_100g': 365
        },
        'market_demand': 'High',
        'storage_life': 6,
        'processing_options': ['Animal Feed', 'Starch', 'Oil Extraction']
    },
    'sugarcane': {
        'name': 'Sugarcane',
        'category': 'Cash Crop',
        'growing_period': 365,
        'water_requirement': 1800,
        'labor_requirement': 300,
        'seed_cost': 25000,
        'fertilizer_cost': 35000,
        'pesticide_cost': 15000,
        'irrigation_cost': 25000,
        'labor_cost': 90000,
        'other_costs': 20000,
        'total_cost': 210000,
        'average_yield': 70000,
        'market_price': 3.5,
        'total_revenue': 245000,
        'profit': 35000,
        'roi': 16.7,
        'soil_type': ['Clay Loam', 'Loamy'],
        'climate': 'Tropical, Subtropical',
        'season': 'Annual',
        'ph_range': [6.0, 8.0],
        'temperature_range': [20, 30],
        'rainfall_requirement': [1000, 1500],
        'nutritional_value': {
            'protein': 0.4,
            'carbohydrates': 13.8,
            'fiber': 0.6,
            'calories_per_100g': 58
        },
        'market_demand': 'Very High',
        'storage_life': 1,
        'processing_options': ['Sugar Production', 'Jaggery', 'Ethanol']
    },
    'cotton': {
        'name': 'Cotton',
        'category': 'Cash Crop',
        'growing_period': 180,
        'water_requirement': 700,
        'labor_requirement': 200,
        'seed_cost': 12000,
        'fertilizer_cost': 25000,
        'pesticide_cost': 20000,
        'irrigation_cost': 15000,
        'labor_cost': 60000,
        'other_costs': 15000,
        'total_cost': 147000,
        'average_yield': 500,
        'market_price': 350,
        'total_revenue': 175000,
        'profit': 28000,
        'roi': 19.0,
        'soil_type': ['Black Cotton Soil', 'Alluvial'],
        'climate': 'Tropical, Semi-arid',
        'season': 'Kharif',
        'ph_range': [5.8, 8.0],
        'temperature_range': [21, 30],
        'rainfall_requirement': [500, 1000],
        'nutritional_value': {
            'protein': 0,
            'carbohydrates': 0,
            'fiber': 0,
            'calories_per_100g': 0
        },
        'market_demand': 'High',
        'storage_life': 24,
        'processing_options': ['Textile', 'Oil Extraction', 'Animal Feed']
    },
    'tomato': {
        'name': 'Tomato',
        'category': 'Vegetable',
        'growing_period': 90,
        'water_requirement': 400,
        'labor_requirement': 250,
        'seed_cost': 15000,
        'fertilizer_cost': 20000,
        'pesticide_cost': 18000,
        'irrigation_cost': 12000,
        'labor_cost': 75000,
        'other_costs': 12000,
        'total_cost': 152000,
        'average_yield': 25000,
        'market_price': 12,
        'total_revenue': 300000,
        'profit': 148000,
        'roi': 97.4,
        'soil_type': ['Loamy', 'Sandy Loam'],
        'climate': 'Tropical, Temperate',
        'season': 'All Season',
        'ph_range': [6.0, 7.0],
        'temperature_range': [20, 25],
        'rainfall_requirement': [300, 500],
        'nutritional_value': {
            'protein': 0.9,
            'carbohydrates': 3.9,
            'fiber': 1.2,
            'calories_per_100g': 18
        },
        'market_demand': 'Very High',
        'storage_life': 1,
        'processing_options': ['Fresh Market', 'Processing', 'Sauce']
    },
    'potato': {
        'name': 'Potato',
        'category': 'Vegetable',
        'growing_period': 90,
        'water_requirement': 350,
        'labor_requirement': 180,
        'seed_cost': 35000,
        'fertilizer_cost': 18000,
        'pesticide_cost': 12000,
        'irrigation_cost': 10000,
        'labor_cost': 54000,
        'other_costs': 10000,
        'total_cost': 139000,
        'average_yield': 20000,
        'market_price': 15,
        'total_revenue': 300000,
        'profit': 161000,
        'roi': 115.8,
        'soil_type': ['Sandy Loam', 'Loamy'],
        'climate': 'Temperate, Cool',
        'season': 'Rabi',
        'ph_range': [5.0, 6.5],
        'temperature_range': [15, 20],
        'rainfall_requirement': [300, 400],
        'nutritional_value': {
            'protein': 2.0,
            'carbohydrates': 17.5,
            'fiber': 2.2,
            'calories_per_100g': 77
        },
        'market_demand': 'Very High',
        'storage_life': 6,
        'processing_options': ['Fresh Market', 'Chips', 'Starch']
    },
    'onion': {
        'name': 'Onion',
        'category': 'Vegetable',
        'growing_period': 120,
        'water_requirement': 350,
        'labor_requirement': 200,
        'seed_cost': 8000,
        'fertilizer_cost': 15000,
        'pesticide_cost': 10000,
        'irrigation_cost': 8000,
        'labor_cost': 60000,
        'other_costs': 8000,
        'total_cost': 109000,
        'average_yield': 15000,
        'market_price': 18,
        'total_revenue': 270000,
        'profit': 161000,
        'roi': 147.7,
        'soil_type': ['Loamy', 'Sandy Loam'],
        'climate': 'Temperate, Semi-arid',
        'season': 'Rabi',
        'ph_range': [6.0, 7.5],
        'temperature_range': [13, 24],
        'rainfall_requirement': [250, 400],
        'nutritional_value': {
            'protein': 1.1,
            'carbohydrates': 9.3,
            'fiber': 1.7,
            'calories_per_100g': 40
        },
        'market_demand': 'Very High',
        'storage_life': 8,
        'processing_options': ['Fresh Market', 'Dehydration', 'Powder']
    },
    'banana': {
        'name': 'Banana',
        'category': 'Fruit',
        'growing_period': 365,
        'water_requirement': 1200,
        'labor_requirement': 250,
        'seed_cost': 40000,
        'fertilizer_cost': 30000,
        'pesticide_cost': 15000,
        'irrigation_cost': 20000,
        'labor_cost': 75000,
        'other_costs': 15000,
        'total_cost': 195000,
        'average_yield': 30000,
        'market_price': 15,
        'total_revenue': 450000,
        'profit': 255000,
        'roi': 130.8,
        'soil_type': ['Loamy', 'Clay Loam'],
        'climate': 'Tropical, Humid',
        'season': 'Annual',
        'ph_range': [6.0, 7.5],
        'temperature_range': [26, 30],
        'rainfall_requirement': [1000, 2000],
        'nutritional_value': {
            'protein': 1.1,
            'carbohydrates': 22.8,
            'fiber': 2.6,
            'calories_per_100g': 89
        },
        'market_demand': 'Very High',
        'storage_life': 1,
        'processing_options': ['Fresh Market', 'Chips', 'Powder']
    },
    'mango': {
        'name': 'Mango',
        'category': 'Fruit',
        'growing_period': 1825,  # 5 years to maturity
        'water_requirement': 800,
        'labor_requirement': 150,
        'seed_cost': 50000,
        'fertilizer_cost': 25000,
        'pesticide_cost': 20000,
        'irrigation_cost': 15000,
        'labor_cost': 45000,
        'other_costs': 20000,
        'total_cost': 175000,
        'average_yield': 8000,
        'market_price': 40,
        'total_revenue': 320000,
        'profit': 145000,
        'roi': 82.9,
        'soil_type': ['Loamy', 'Sandy Loam'],
        'climate': 'Tropical, Subtropical',
        'season': 'Perennial',
        'ph_range': [5.5, 7.5],
        'temperature_range': [24, 30],
        'rainfall_requirement': [750, 2500],
        'nutritional_value': {
            'protein': 0.8,
            'carbohydrates': 15.0,
            'fiber': 1.6,
            'calories_per_100g': 60
        },
        'market_demand': 'High',
        'storage_life': 2,
        'processing_options': ['Fresh Market', 'Pulp', 'Juice']
    }
}

# Market trends and seasonal price variations
MARKET_TRENDS = {
    'rice': {'peak_months': [10, 11, 12], 'low_months': [4, 5, 6], 'price_variation': 15},
    'wheat': {'peak_months': [3, 4, 5], 'low_months': [10, 11, 12], 'price_variation': 12},
    'maize': {'peak_months': [1, 2, 3], 'low_months': [7, 8, 9], 'price_variation': 18},
    'sugarcane': {'peak_months': [12, 1, 2], 'low_months': [6, 7, 8], 'price_variation': 8},
    'cotton': {'peak_months': [3, 4, 5], 'low_months': [9, 10, 11], 'price_variation': 25},
    'tomato': {'peak_months': [12, 1, 2], 'low_months': [6, 7, 8], 'price_variation': 40},
    'potato': {'peak_months': [4, 5, 6], 'low_months': [12, 1, 2], 'price_variation': 30},
    'onion': {'peak_months': [7, 8, 9], 'low_months': [2, 3, 4], 'price_variation': 35},
    'banana': {'peak_months': [5, 6, 7], 'low_months': [11, 12, 1], 'price_variation': 20},
    'mango': {'peak_months': [4, 5, 6], 'low_months': [10, 11, 12], 'price_variation': 50}
}

# Regional suitability data
REGIONAL_SUITABILITY = {
    'rice': ['West Bengal', 'Punjab', 'Uttar Pradesh', 'Andhra Pradesh', 'Tamil Nadu'],
    'wheat': ['Punjab', 'Haryana', 'Uttar Pradesh', 'Madhya Pradesh', 'Rajasthan'],
    'maize': ['Karnataka', 'Andhra Pradesh', 'Tamil Nadu', 'Rajasthan', 'Maharashtra'],
    'sugarcane': ['Uttar Pradesh', 'Maharashtra', 'Karnataka', 'Tamil Nadu', 'Gujarat'],
    'cotton': ['Gujarat', 'Maharashtra', 'Telangana', 'Punjab', 'Haryana'],
    'tomato': ['Karnataka', 'Andhra Pradesh', 'Maharashtra', 'Madhya Pradesh', 'Gujarat'],
    'potato': ['Uttar Pradesh', 'Punjab', 'Bihar', 'Gujarat', 'Madhya Pradesh'],
    'onion': ['Maharashtra', 'Gujarat', 'Madhya Pradesh', 'Karnataka', 'Rajasthan'],
    'banana': ['Tamil Nadu', 'Gujarat', 'Maharashtra', 'Andhra Pradesh', 'Karnataka'],
    'mango': ['Uttar Pradesh', 'Andhra Pradesh', 'Karnataka', 'Gujarat', 'Bihar']
}
