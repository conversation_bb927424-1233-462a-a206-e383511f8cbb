{% extends 'layout.html' %}

{% block body %}
<!-- Presentation De<PERSON>e Banner -->
<div class="demo-marquee-container">
	<marquee class="demo-marquee" behavior="scroll" direction="left" scrollamount="8" onmouseover="this.stop();" onmouseout="this.start();">
		<span class="marquee-demo-text">
			🎓 PROUDLY DEVELOPED BY AIML STUDENTS 🎓 PES COLLEGE OF ENGINEERING, MANDYA 🎓
			DEPARTMENT OF ARTIFICIAL INTELLIGENCE & MACHINE LEARNING 🎓
			SHOWCASING INNOVATION IN AGRICULTURE TECHNOLOGY 🎓
			AI-POWERED SMART FARMING SOLUTIONS 🎓
			ACADEMIC EXCELLENCE IN ACTION 🎓
		</span>
	</marquee>
</div>

<!-- Hero Section -->
<section class="farmer-hero" style="background: linear-gradient(135deg, #87ceeb 0%, #98fb98 50%, #90ee90 100%); min-height: 100vh; position: relative; padding: 100px 0;">
	<!-- Farm Background Pattern -->
	<div class="farm-pattern"></div>

	<div class="container">
		<div class="row align-items-center min-vh-100">
			<div class="col-lg-6" data-aos="fade-right">
				<div class="farmer-content">
					<!-- Simple Welcome -->
					<div class="welcome-badge mb-4" data-aos="fade-up">
						<span class="welcome-icon">🌾</span>
						<span class="welcome-text">Welcome Farmers!</span>
					</div>

					<h1 class="farmer-title mb-4" data-aos="fade-up" data-aos-delay="200">
						<span class="main-title">AGRI HUB</span>
						<span class="sub-title">Your Smart Farming Assistant</span>
					</h1>

					<p class="farmer-description mb-5" data-aos="fade-up" data-aos-delay="400">
						🌱 Get the best crop recommendations for your soil<br>
						🔬 Detect plant diseases instantly with your phone<br>
						💧 Know exactly what fertilizers your crops need<br>
						🌤️ Get weather advice for perfect farming timing<br>
						💰 Compare crops to maximize your profits
					</p>

					<!-- Simple Action Buttons -->
					<div class="farmer-actions mb-5" data-aos="fade-up" data-aos-delay="600">
						<a href="{{ url_for('crop_recommend') }}" class="farmer-btn primary">
							<i class="fas fa-seedling"></i>
							<span>Find Best Crops</span>
						</a>
						<a href="{{ url_for('disease_prediction') }}" class="farmer-btn secondary">
							<i class="fas fa-camera"></i>
							<span>Check Plant Health</span>
						</a>
					</div>

					<!-- Trust Indicators -->
					<div class="trust-indicators" data-aos="fade-up" data-aos-delay="800">
						<div class="trust-item">
							<div class="trust-number">10,000+</div>
							<div class="trust-label">Happy Farmers</div>
						</div>
						<div class="trust-item">
							<div class="trust-number">99%</div>
							<div class="trust-label">Accurate Results</div>
						</div>
						<div class="trust-item">
							<div class="trust-number">Free</div>
							<div class="trust-label">Always Free</div>
						</div>
					</div>
				</div>
			</div>

			<div class="col-lg-6" data-aos="fade-left" data-aos-delay="200">
				<div class="farmer-visual">
					<!-- Simple Feature Cards -->
					<div class="feature-grid">
						<div class="feature-card-simple" data-aos="zoom-in" data-aos-delay="300">
							<div class="feature-icon-simple">🌾</div>
							<h4>Smart Crops</h4>
							<p>Find the best crops for your land</p>
						</div>
						<div class="feature-card-simple" data-aos="zoom-in" data-aos-delay="400">
							<div class="feature-icon-simple">🔬</div>
							<h4>Disease Check</h4>
							<p>Take a photo, get instant diagnosis</p>
						</div>
						<div class="feature-card-simple" data-aos="zoom-in" data-aos-delay="500">
							<div class="feature-icon-simple">💧</div>
							<h4>Soil Care</h4>
							<p>Know what your soil needs</p>
						</div>
						<div class="feature-card-simple" data-aos="zoom-in" data-aos-delay="600">
							<div class="feature-icon-simple">🌤️</div>
							<h4>Weather Guide</h4>
							<p>Perfect timing for farming</p>
						</div>
					</div>

					<!-- Farmer Testimonial -->
					<div class="farmer-testimonial" data-aos="fade-up" data-aos-delay="700">
						<div class="testimonial-content">
							<p>"This app helped me increase my crop yield by 40%! Easy to use and very helpful."</p>
							<div class="farmer-info">
								<strong>- Ravi Kumar, Farmer from Karnataka</strong>
							</div>
						</div>
					</div>
				</div>
			</div>
				<div class="hero-visual position-relative">
					<!-- Main Dashboard Mockup -->
					<div class="dashboard-mockup" data-aos="zoom-in" data-aos-delay="400">
						<div class="dashboard-header">
							<div class="dashboard-title">
								<img src="{{ url_for('static', filename='images/logo.png') }}" alt="AGRI HUB Logo" style="height: 20px; width: auto; margin-right: 8px;">
								<i class="fas fa-leaf me-2"></i>AGRI HUB Dashboard
							</div>
							<div class="dashboard-status">
								<span class="status-dot"></span>Live System
							</div>
						</div>
						<div class="dashboard-body">
							<div class="metric-row">
								<div class="metric-card">
									<div class="metric-icon">
										<i class="fas fa-seedling text-success"></i>
									</div>
									<div class="metric-info">
										<div class="metric-value">1,247</div>
										<div class="metric-label">Crops Analyzed</div>
									</div>
								</div>
								<div class="metric-card">
									<div class="metric-icon">
										<i class="fas fa-bug text-danger"></i>
									</div>
									<div class="metric-info">
										<div class="metric-value">98.5%</div>
										<div class="metric-label">Disease Detection</div>
									</div>
								</div>
							</div>
							<div class="chart-area">
								<div class="chart-title">Yield Improvement</div>
								<div class="chart-bars">
									<div class="bar" style="height: 60%;"></div>
									<div class="bar" style="height: 80%;"></div>
									<div class="bar" style="height: 95%;"></div>
									<div class="bar" style="height: 75%;"></div>
									<div class="bar" style="height: 90%;"></div>
								</div>
							</div>
						</div>
					</div>
					
					<!-- Floating Feature Cards -->
					<div class="floating-feature top-right" data-aos="fade-up" data-aos-delay="600">
						<div class="feature-icon">
							<i class="fas fa-brain text-primary"></i>
						</div>
						<div class="feature-content">
							<div class="feature-title">AI Analysis</div>
							<div class="feature-desc">Real-time Processing</div>
						</div>
					</div>
					
					<div class="floating-feature bottom-left" data-aos="fade-up" data-aos-delay="800">
						<div class="feature-icon">
							<i class="fas fa-cloud-sun text-warning"></i>
						</div>
						<div class="feature-content">
							<div class="feature-title">Weather AI</div>
							<div class="feature-desc">7-Day Forecasting</div>
						</div>
					</div>
					
					<div class="floating-feature middle-right" data-aos="fade-up" data-aos-delay="1000">
						<div class="feature-icon">
							<i class="fas fa-microscope text-info"></i>
						</div>
						<div class="feature-content">
							<div class="feature-title">Disease Detection</div>
							<div class="feature-desc">Instant Analysis</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>

<!-- How It Helps Farmers Section -->
<section id="features" class="farmer-features py-5" style="background: #f0f8f0;">
	<div class="container">
		<div class="text-center mb-5" data-aos="fade-up">
			<h2 class="farmer-section-title">
				🌟 How AGRI HUB Helps You Farm Better
			</h2>
			<p class="farmer-section-desc">
				Simple tools that make farming easier and more profitable
			</p>
		</div>
		
		<div class="row g-4">
			<div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="100">
				<div class="farmer-feature-card">
					<div class="farmer-feature-icon">🌾</div>
					<h4 class="farmer-feature-title">Which Crop to Grow?</h4>
					<p class="farmer-feature-desc">
						Tell us about your soil and location. We'll suggest the best crops that will give you maximum profit.
					</p>
					<a href="{{ url_for('crop_recommend') }}" class="farmer-feature-btn">
						Get Suggestions →
					</a>
				</div>
			</div>

			<div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="200">
				<div class="farmer-feature-card">
					<div class="farmer-feature-icon">📱</div>
					<h4 class="farmer-feature-title">Is My Plant Sick?</h4>
					<p class="farmer-feature-desc">
						Take a photo of your plant with your phone. We'll tell you if it's healthy or what disease it has.
					</p>
					<a href="{{ url_for('disease_prediction') }}" class="farmer-feature-btn">
						Check Plant Health →
					</a>
				</div>
			</div>

			<div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="300">
				<div class="farmer-feature-card">
					<div class="farmer-feature-icon">💧</div>
					<h4 class="farmer-feature-title">What Fertilizer to Use?</h4>
					<p class="farmer-feature-desc">
						Get exact fertilizer recommendations for your crops. Know what nutrients your soil needs.
					</p>
					<a href="{{ url_for('fertilizer_recommendation') }}" class="farmer-feature-btn">
						Get Fertilizer Advice →
					</a>
				</div>
			</div>

			<div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="400">
				<div class="farmer-feature-card">
					<div class="farmer-feature-icon">🌤️</div>
					<h4 class="farmer-feature-title">When to Plant & Harvest?</h4>
					<p class="farmer-feature-desc">
						Get weather forecasts and know the best days for planting, watering, and harvesting your crops.
					</p>
					<a href="{{ url_for('weather_recommendations') }}" class="farmer-feature-btn">
						Check Weather →
					</a>
				</div>
			</div>

			<div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="500">
				<div class="farmer-feature-card">
					<div class="farmer-feature-icon">💰</div>
					<h4 class="farmer-feature-title">Which Crop Gives More Profit?</h4>
					<p class="farmer-feature-desc">
						Compare different crops and see which one will make you more money based on market prices.
					</p>
					<a href="{{ url_for('crop_comparison') }}" class="farmer-feature-btn">
						Compare Profits →
					</a>
				</div>
			</div>

			<div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="600">
				<div class="farmer-feature-card">
					<div class="farmer-feature-icon">🤖</div>
					<h4 class="farmer-feature-title">Ask Any Farming Question</h4>
					<p class="farmer-feature-desc">
						Chat with our AI assistant anytime. Ask any question about farming and get instant answers.
					</p>
					<a href="{{ url_for('chatbot') }}" class="farmer-feature-btn">
						Start Chatting →
					</a>
				</div>
			</div>
		</div>
	</div>
</section>

<!-- Technology Stack Section -->
<section class="tech-section py-5" style="background: linear-gradient(135deg, #1a4d3a 0%, #2d5a27 100%);">
	<div class="container">
		<div class="text-center mb-5" data-aos="fade-up">
			<h2 class="display-4 fw-bold text-white mb-3">
				⚡ Powered by Advanced Technology
			</h2>
			<p class="lead text-light opacity-75">
				Built with cutting-edge AI and machine learning technologies
			</p>
		</div>
		
		<div class="row g-4 text-center">
			<div class="col-lg-3 col-md-6" data-aos="zoom-in" data-aos-delay="100">
				<div class="tech-item">
					<div class="tech-icon">
						<i class="fab fa-python"></i>
					</div>
					<h5 class="tech-title">Python & Flask</h5>
					<p class="tech-desc">Robust backend framework</p>
				</div>
			</div>
			
			<div class="col-lg-3 col-md-6" data-aos="zoom-in" data-aos-delay="200">
				<div class="tech-item">
					<div class="tech-icon">
						<i class="fas fa-brain"></i>
					</div>
					<h5 class="tech-title">PyTorch</h5>
					<p class="tech-desc">Deep learning models</p>
				</div>
			</div>
			
			<div class="col-lg-3 col-md-6" data-aos="zoom-in" data-aos-delay="300">
				<div class="tech-item">
					<div class="tech-icon">
						<i class="fab fa-google"></i>
					</div>
					<h5 class="tech-title">Google AI</h5>
					<p class="tech-desc">Gemini API integration</p>
				</div>
			</div>
			
			<div class="col-lg-3 col-md-6" data-aos="zoom-in" data-aos-delay="400">
				<div class="tech-item">
					<div class="tech-icon">
						<i class="fas fa-chart-bar"></i>
					</div>
					<h5 class="tech-title">Machine Learning</h5>
					<p class="tech-desc">Advanced algorithms</p>
				</div>
			</div>
		</div>
	</div>
</section>

<!-- Development Team Section -->
<section class="team-section py-5" style="background: linear-gradient(135deg, #ffc107 0%, #ffed4e 100%);">
	<div class="container">
		<div class="row align-items-center">
			<div class="col-lg-8 mx-auto text-center" data-aos="fade-up">
				<div class="team-content">
					<div class="team-icon mb-4">
						<i class="fas fa-graduation-cap" style="font-size: 4rem; color: #1a4d3a;"></i>
					</div>
					<h2 class="team-title mb-3" style="color: #1a4d3a; font-weight: 800;">
						🎓 Proudly Developed by AIML Students
					</h2>
					<h4 class="team-subtitle mb-4" style="color: #2d5a27; font-weight: 600;">
						PES College of Engineering, Mandya
					</h4>
					<p class="team-description mb-4" style="color: #1a4d3a; font-size: 1.1rem; line-height: 1.7;">
						This comprehensive Agriculture Hub platform represents the culmination of advanced learning in
						<strong>Artificial Intelligence and Machine Learning</strong>. Our student team has successfully
						integrated cutting-edge AI technologies including computer vision, natural language processing,
						and predictive analytics to create a real-world solution for modern agriculture challenges.
					</p>
					<div class="team-features">
						<div class="row g-3">
							<div class="col-md-4">
								<div class="feature-highlight">
									<i class="fas fa-brain mb-2" style="font-size: 2rem; color: #1a4d3a;"></i>
									<h6 style="color: #1a4d3a; font-weight: 600;">AI/ML Implementation</h6>
									<p style="color: #2d5a27; font-size: 0.9rem;">Advanced algorithms and neural networks</p>
								</div>
							</div>
							<div class="col-md-4">
								<div class="feature-highlight">
									<i class="fas fa-code mb-2" style="font-size: 2rem; color: #1a4d3a;"></i>
									<h6 style="color: #1a4d3a; font-weight: 600;">Full-Stack Development</h6>
									<p style="color: #2d5a27; font-size: 0.9rem;">End-to-end platform development</p>
								</div>
							</div>
							<div class="col-md-4">
								<div class="feature-highlight">
									<i class="fas fa-seedling mb-2" style="font-size: 2rem; color: #1a4d3a;"></i>
									<h6 style="color: #1a4d3a; font-weight: 600;">Real-World Impact</h6>
									<p style="color: #2d5a27; font-size: 0.9rem;">Solving agricultural challenges</p>
								</div>
							</div>
						</div>
					</div>
					<div class="team-badge mt-4">
						<span class="badge" style="background: #1a4d3a; color: #ffc107; padding: 12px 24px; border-radius: 50px; font-size: 1rem; font-weight: 600;">
							<i class="fas fa-award me-2"></i>Academic Excellence in AI/ML
						</span>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>

<style>
/* Farmer-Friendly Design Styles */
.farmer-hero {
	position: relative;
	overflow: hidden;
}

.farm-pattern {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-image:
		radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 2px, transparent 2px),
		radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 2px, transparent 2px);
	background-size: 50px 50px;
	opacity: 0.3;
}

.welcome-badge {
	background: rgba(255, 255, 255, 0.9);
	border-radius: 50px;
	padding: 12px 24px;
	display: inline-flex;
	align-items: center;
	gap: 10px;
	box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.welcome-icon {
	font-size: 1.5rem;
}

.welcome-text {
	font-weight: 700;
	color: #2d5a27;
	font-size: 1.1rem;
}

.farmer-title {
	text-align: left;
}

.main-title {
	display: block;
	font-size: 4rem;
	font-weight: 900;
	color: #2d5a27;
	text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
	line-height: 1;
}

.sub-title {
	display: block;
	font-size: 1.8rem;
	font-weight: 600;
	color: #4a7c59;
	margin-top: 10px;
}

.farmer-description {
	font-size: 1.2rem;
	line-height: 1.8;
	color: #2d5a27;
	font-weight: 500;
}

.farmer-actions {
	display: flex;
	gap: 20px;
	flex-wrap: wrap;
}

.farmer-btn {
	display: flex;
	align-items: center;
	gap: 12px;
	padding: 18px 30px;
	border-radius: 15px;
	text-decoration: none;
	font-weight: 700;
	font-size: 1.1rem;
	transition: all 0.3s ease;
	box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.farmer-btn.primary {
	background: linear-gradient(135deg, #4a7c59, #7fb069);
	color: white;
}

.farmer-btn.secondary {
	background: linear-gradient(135deg, #87ceeb, #98fb98);
	color: #2d5a27;
}

.farmer-btn:hover {
	transform: translateY(-3px);
	box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
	color: inherit;
}

.farmer-btn i {
	font-size: 1.3rem;
}

.trust-indicators {
	display: flex;
	gap: 30px;
	flex-wrap: wrap;
}

.trust-item {
	text-align: center;
	background: rgba(255, 255, 255, 0.8);
	padding: 20px;
	border-radius: 15px;
	box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.trust-number {
	font-size: 2rem;
	font-weight: 900;
	color: #2d5a27;
	line-height: 1;
}

.trust-label {
	font-size: 0.9rem;
	color: #4a7c59;
	font-weight: 600;
	margin-top: 5px;
}

/* Demo Presentation Marquee Styles */
.demo-marquee-container {
	position: fixed;
	top: 85px;
	left: 0;
	width: 100%;
	z-index: 999;
	background: linear-gradient(135deg, #2d5a27 0%, #4a7c59 50%, #7fb069 100%);
	border-top: 3px solid #a7c957;
	border-bottom: 3px solid #a7c957;
	box-shadow: 0 8px 32px rgba(45, 90, 39, 0.6);
	backdrop-filter: blur(15px);
	animation: containerPulse 4s ease-in-out infinite alternate;
}

.demo-marquee {
	background: transparent;
	color: white;
	font-family: 'Inter', sans-serif;
	font-weight: 800;
	font-size: 1.3rem;
	padding: 15px 0;
	text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
	letter-spacing: 2px;
	line-height: 1.2;
}

.marquee-close {
	position: absolute;
	top: 50%;
	right: 15px;
	transform: translateY(-50%);
	background: #2d5a27;
	color: white;
	border: none;
	border-radius: 50%;
	width: 30px;
	height: 30px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	font-size: 0.8rem;
	transition: all 0.3s ease;
	box-shadow: 0 2px 8px rgba(45, 90, 39, 0.3);
}

.marquee-close:hover {
	background: #1a4d3a;
	transform: translateY(-50%) scale(1.1);
	box-shadow: 0 4px 12px rgba(26, 77, 58, 0.5);
}

.marquee-content {
	display: flex;
	animation: marqueeScroll 25s linear infinite;
	white-space: nowrap;
}

.marquee-text {
	font-size: 1.1rem;
	font-weight: 700;
	color: #1a4d3a;
	padding: 12px 0;
	text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.9);
	display: inline-block;
	min-width: 100%;
}

@keyframes marqueeScroll {
	0% {
		transform: translateX(100%);
	}
	100% {
		transform: translateX(-100%);
	}
}

@keyframes marqueeFloat {
	0% {
		transform: translateY(0px);
		box-shadow: 0 4px 15px rgba(127, 176, 105, 0.4);
	}
	100% {
		transform: translateY(-3px);
		box-shadow: 0 8px 25px rgba(127, 176, 105, 0.6);
	}
}

.marquee-demo-text {
	background: linear-gradient(45deg, #ffffff, #a7c957, #ffffff, #7fb069);
	background-size: 400% 400%;
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
	animation: textShimmer 3s ease-in-out infinite;
	display: inline-block;
	font-weight: 900;
	text-transform: uppercase;
}

@keyframes containerPulse {
	0% {
		box-shadow: 0 8px 32px rgba(45, 90, 39, 0.6);
		border-top-color: #a7c957;
		border-bottom-color: #a7c957;
	}
	100% {
		box-shadow: 0 12px 48px rgba(127, 176, 105, 0.8);
		border-top-color: #7fb069;
		border-bottom-color: #7fb069;
	}
}

@keyframes textShimmer {
	0% {
		background-position: 0% 50%;
	}
	50% {
		background-position: 100% 50%;
	}
	100% {
		background-position: 0% 50%;
	}
}

/* Responsive Design for Farmers */
@media (max-width: 768px) {
	.demo-marquee-container {
		top: 70px;
	}

	.demo-marquee {
		font-size: 1.1rem;
		padding: 12px 0;
		letter-spacing: 1px;
	}

	.main-title {
		font-size: 3rem;
	}

	.sub-title {
		font-size: 1.4rem;
	}

	.farmer-description {
		font-size: 1.1rem;
	}

	.farmer-actions {
		flex-direction: column;
	}

	.farmer-btn {
		justify-content: center;
		text-align: center;
	}

	.trust-indicators {
		justify-content: center;
	}

	.feature-grid {
		grid-template-columns: 1fr;
		gap: 15px;
	}
}

@media (max-width: 480px) {
	.demo-marquee {
		font-size: 0.9rem;
		padding: 10px 0;
		letter-spacing: 0.5px;
	}

	.marquee-demo-text {
		font-weight: 800;
	}

	.main-title {
		font-size: 2.5rem;
	}

	.sub-title {
		font-size: 1.2rem;
	}

	.farmer-description {
		font-size: 1rem;
	}

	.farmer-btn {
		padding: 15px 20px;
		font-size: 1rem;
	}

	.trust-item {
		padding: 15px;
	}

	.trust-number {
		font-size: 1.5rem;
	}

	.farmer-feature-card {
		padding: 20px;
	}

	.farmer-feature-icon {
		font-size: 3rem;
	}

	.farmer-feature-title {
		font-size: 1.1rem;
	}
}

/* Farmer Visual Elements */
.feature-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 20px;
	margin-bottom: 30px;
}

.feature-card-simple {
	background: rgba(255, 255, 255, 0.9);
	padding: 25px;
	border-radius: 15px;
	text-align: center;
	box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
}

.feature-card-simple:hover {
	transform: translateY(-5px);
	box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.feature-icon-simple {
	font-size: 3rem;
	margin-bottom: 15px;
}

.feature-card-simple h4 {
	color: #2d5a27;
	font-weight: 700;
	margin-bottom: 10px;
	font-size: 1.1rem;
}

.feature-card-simple p {
	color: #4a7c59;
	font-size: 0.9rem;
	margin: 0;
}

.farmer-testimonial {
	background: rgba(255, 255, 255, 0.9);
	padding: 25px;
	border-radius: 15px;
	box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
	border-left: 5px solid #4a7c59;
}

.testimonial-content p {
	font-style: italic;
	color: #2d5a27;
	font-size: 1.1rem;
	margin-bottom: 15px;
}

.farmer-info {
	color: #4a7c59;
	font-weight: 600;
}

/* Farmer-Friendly Feature Cards */
.farmer-section-title {
	font-size: 2.5rem;
	font-weight: 800;
	color: #2d5a27;
	margin-bottom: 15px;
}

.farmer-section-desc {
	font-size: 1.2rem;
	color: #4a7c59;
	font-weight: 500;
}

.farmer-feature-card {
	background: white;
	padding: 30px;
	border-radius: 20px;
	text-align: center;
	box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
	border: 3px solid transparent;
	height: 100%;
}

.farmer-feature-card:hover {
	transform: translateY(-10px);
	box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
	border-color: #7fb069;
}

.farmer-feature-icon {
	font-size: 4rem;
	margin-bottom: 20px;
	display: block;
}

.farmer-feature-title {
	color: #2d5a27;
	font-weight: 700;
	margin-bottom: 15px;
	font-size: 1.3rem;
}

.farmer-feature-desc {
	color: #4a7c59;
	font-size: 1rem;
	line-height: 1.6;
	margin-bottom: 20px;
}

.farmer-feature-btn {
	background: linear-gradient(135deg, #4a7c59, #7fb069);
	color: white;
	padding: 12px 25px;
	border-radius: 25px;
	text-decoration: none;
	font-weight: 600;
	display: inline-block;
	transition: all 0.3s ease;
}

.farmer-feature-btn:hover {
	background: linear-gradient(135deg, #2d5a27, #4a7c59);
	color: white;
	transform: scale(1.05);
}

/* Hero Section Styles */
.project-badge {
	background: rgba(255, 255, 255, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.2);
	border-radius: 50px;
	padding: 12px 24px;
	display: inline-flex;
	align-items: center;
	gap: 10px;
	backdrop-filter: blur(10px);
}

.badge-icon {
	font-size: 1.2rem;
}

.badge-text {
	font-weight: 600;
	font-size: 0.9rem;
}

.demo-badge {
	background: linear-gradient(135deg, #ff6b6b, #ee5a24);
	border: 2px solid #ffffff;
	border-radius: 50px;
	padding: 12px 24px;
	display: inline-flex;
	align-items: center;
	gap: 10px;
	position: relative;
	box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
	animation: demoBadgeFloat 2s ease-in-out infinite alternate;
}

.demo-icon {
	font-size: 1.2rem;
	animation: demoIconSpin 3s linear infinite;
}

.demo-text {
	font-weight: 800;
	font-size: 0.9rem;
	color: white;
	text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
	letter-spacing: 1px;
}

.demo-pulse {
	position: absolute;
	top: -2px;
	left: -2px;
	right: -2px;
	bottom: -2px;
	border: 2px solid rgba(255, 255, 255, 0.6);
	border-radius: 50px;
	animation: demoPulse 2s ease-in-out infinite;
}

@keyframes demoBadgeFloat {
	0% {
		transform: translateY(0px);
		box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
	}
	100% {
		transform: translateY(-3px);
		box-shadow: 0 12px 35px rgba(255, 107, 107, 0.6);
	}
}

@keyframes demoIconSpin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

@keyframes demoPulse {
	0% {
		transform: scale(1);
		opacity: 1;
	}
	100% {
		transform: scale(1.05);
		opacity: 0;
	}
}

.developer-badge {
	background: rgba(255, 215, 0, 0.15);
	border: 1px solid rgba(255, 215, 0, 0.3);
	border-radius: 50px;
	padding: 12px 24px;
	display: inline-block;
	text-align: center;
	backdrop-filter: blur(10px);
	box-shadow: 0 4px 15px rgba(255, 215, 0, 0.2);
}

.dev-icon {
	font-size: 1.2rem;
	margin-right: 8px;
}

.dev-text {
	font-weight: 700;
	font-size: 1rem;
	color: #ffd700;
	display: block;
	margin-bottom: 2px;
}

.dev-subtitle {
	font-weight: 500;
	font-size: 0.8rem;
	color: rgba(255, 255, 255, 0.8);
	display: block;
}

.hero-title {
	font-size: 4rem;
	font-weight: 900;
	line-height: 1.1;
}

.title-line-1 {
	display: block;
	font-size: 2.5rem;
	font-weight: 400;
	opacity: 0.9;
}

.title-line-2 {
	display: block;
	background: linear-gradient(45deg, #a7c957, #7fb069, #4a7c59);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
	text-shadow: 0 0 30px rgba(167, 201, 87, 0.3);
}

.agri-text {
	margin-right: 0.2em;
}

.hub-text {
	color: #7fb069 !important;
	-webkit-text-fill-color: #7fb069 !important;
}

.hero-subtitle {
	font-size: 1.5rem;
	font-weight: 600;
	color: #a7c957;
}

.hero-description {
	font-size: 1.1rem;
	line-height: 1.7;
	opacity: 0.9;
}

.hero-stats {
	display: flex;
	gap: 2rem;
	flex-wrap: wrap;
}

.stat-item {
	text-align: center;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 15px;
	padding: 20px 15px;
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.2);
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.stat-item:hover {
	transform: translateY(-5px);
	background: rgba(255, 255, 255, 0.15);
	box-shadow: 0 10px 30px rgba(167, 201, 87, 0.3);
}

.stat-item::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 3px;
	background: linear-gradient(90deg, #a7c957, #7fb069);
	border-radius: 15px 15px 0 0;
}

.stat-number {
	font-size: 2.2rem;
	font-weight: 900;
	color: #a7c957;
	line-height: 1;
	text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
	font-size: 0.9rem;
	opacity: 0.9;
	margin-top: 8px;
	font-weight: 600;
}

.stat-icon {
	margin-top: 10px;
	font-size: 1.2rem;
	color: #7fb069;
	opacity: 0.8;
}

.btn-hero-primary {
	background: linear-gradient(45deg, #a7c957, #7fb069);
	border: none;
	color: #1a4d3a;
	font-weight: 700;
	padding: 15px 30px;
	border-radius: 50px;
	transition: all 0.3s ease;
	box-shadow: 0 4px 15px rgba(167, 201, 87, 0.3);
}

.btn-hero-primary:hover {
	transform: translateY(-2px);
	box-shadow: 0 10px 25px rgba(167, 201, 87, 0.5);
	color: #1a4d3a;
	background: linear-gradient(45deg, #7fb069, #4a7c59);
}

.btn-hero-secondary {
	background: transparent;
	border: 2px solid rgba(255, 255, 255, 0.3);
	color: white;
	font-weight: 600;
	padding: 13px 28px;
	border-radius: 50px;
	transition: all 0.3s ease;
}

.btn-hero-secondary:hover {
	background: rgba(255, 255, 255, 0.1);
	border-color: rgba(255, 255, 255, 0.5);
	color: white;
	transform: translateY(-2px);
}

/* Dashboard Mockup */
.dashboard-mockup {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20px;
	padding: 20px;
	box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
	backdrop-filter: blur(10px);
}

.dashboard-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;
	padding-bottom: 15px;
	border-bottom: 1px solid #e9ecef;
}

.dashboard-title {
	font-weight: 700;
	color: #1a4d3a;
	font-size: 1.1rem;
}

.dashboard-status {
	display: flex;
	align-items: center;
	gap: 8px;
	font-size: 0.9rem;
	color: #28a745;
	font-weight: 600;
}

.status-dot {
	width: 8px;
	height: 8px;
	background: #28a745;
	border-radius: 50%;
	animation: pulse 2s infinite;
}

.metric-row {
	display: flex;
	gap: 15px;
	margin-bottom: 20px;
}

.metric-card {
	flex: 1;
	background: #f8f9fa;
	border-radius: 12px;
	padding: 15px;
	display: flex;
	align-items: center;
	gap: 12px;
}

.metric-icon i {
	font-size: 1.5rem;
}

.metric-value {
	font-size: 1.3rem;
	font-weight: 700;
	color: #1a4d3a;
	line-height: 1;
}

.metric-label {
	font-size: 0.8rem;
	color: #6c757d;
	margin-top: 2px;
}

.chart-area {
	background: #f8f9fa;
	border-radius: 12px;
	padding: 15px;
}

.chart-title {
	font-size: 0.9rem;
	font-weight: 600;
	color: #1a4d3a;
	margin-bottom: 10px;
}

.chart-bars {
	display: flex;
	align-items: end;
	gap: 8px;
	height: 60px;
}

.bar {
	flex: 1;
	background: linear-gradient(to top, #7fb069, #4a7c59);
	border-radius: 4px 4px 0 0;
	animation: growUp 2s ease-out;
}

/* Floating Features */
.floating-feature {
	position: absolute;
	background: rgba(255, 255, 255, 0.95);
	border-radius: 15px;
	padding: 15px;
	display: flex;
	align-items: center;
	gap: 12px;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
	backdrop-filter: blur(10px);
	min-width: 200px;
}

.top-right {
	top: 10%;
	right: -5%;
}

.bottom-left {
	bottom: 20%;
	left: -10%;
}

.middle-right {
	top: 50%;
	right: -8%;
}

.feature-icon {
	width: 40px;
	height: 40px;
	border-radius: 10px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #f8f9fa;
}

.feature-icon i {
	font-size: 1.2rem;
}

.feature-title {
	font-weight: 700;
	color: #1a4d3a;
	font-size: 0.9rem;
	line-height: 1;
}

.feature-desc {
	font-size: 0.8rem;
	color: #6c757d;
	margin-top: 2px;
}

/* Feature Cards */
.feature-card {
	background: white;
	border-radius: 20px;
	padding: 30px;
	text-align: center;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
	border: 1px solid #f0f0f0;
}

.feature-card:hover {
	transform: translateY(-10px);
	box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.feature-card-icon {
	width: 80px;
	height: 80px;
	margin: 0 auto 20px;
	background: linear-gradient(45deg, #7fb069, #4a7c59);
	border-radius: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.feature-card-icon i {
	font-size: 2rem;
	color: white;
}

.feature-card-title {
	font-size: 1.3rem;
	font-weight: 700;
	color: #1a4d3a;
	margin-bottom: 15px;
}

.feature-card-desc {
	color: #6c757d;
	line-height: 1.6;
	margin-bottom: 20px;
}

.feature-card-link {
	color: #7fb069;
	text-decoration: none;
	font-weight: 600;
	transition: all 0.3s ease;
}

.feature-card-link:hover {
	color: #4a7c59;
}

/* Tech Section */
.tech-item {
	color: white;
}

.tech-icon {
	width: 80px;
	height: 80px;
	margin: 0 auto 20px;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 1px solid rgba(255, 255, 255, 0.2);
}

.tech-icon i {
	font-size: 2rem;
	color: #ffd700;
}

.tech-title {
	font-weight: 700;
	margin-bottom: 10px;
}

.tech-desc {
	opacity: 0.8;
	font-size: 0.9rem;
}

/* Animations */
@keyframes pulse {
	0%, 100% { opacity: 1; }
	50% { opacity: 0.5; }
}

@keyframes growUp {
	from { height: 0; }
	to { height: inherit; }
}

@keyframes float {
	0%, 100% { transform: translateY(0px); }
	50% { transform: translateY(-10px); }
}

/* Responsive */
@media (max-width: 768px) {
	.hero-title {
		font-size: 2.5rem;
	}
	
	.title-line-1 {
		font-size: 1.8rem;
	}
	
	.hero-stats {
		gap: 1rem;
	}
	
	.floating-feature {
		position: relative;
		top: auto !important;
		right: auto !important;
		bottom: auto !important;
		left: auto !important;
		margin: 10px 0;
	}
}
</style>

<script>
// Demo Presentation Enhancements
document.addEventListener('DOMContentLoaded', function() {
	// Add presentation mode indicator
	const presentationMode = document.createElement('div');
	presentationMode.innerHTML = `
		<div style="position: fixed; bottom: 20px; right: 20px; z-index: 1000;
					background: linear-gradient(135deg, #2d5a27, #4a7c59);
					color: white; padding: 10px 20px; border-radius: 25px;
					font-weight: 600; font-size: 0.9rem; box-shadow: 0 4px 15px rgba(45, 90, 39, 0.4);
					animation: presentationPulse 2s ease-in-out infinite alternate;">
			<i class="fas fa-presentation-screen me-2"></i>PRESENTATION MODE
		</div>
	`;
	document.body.appendChild(presentationMode);

	// Enhanced marquee interactions
	const marqueeContainer = document.querySelector('.demo-marquee-container');
	const marquee = document.querySelector('.demo-marquee');

	if (marqueeContainer && marquee) {
		// Add click to pause/resume functionality
		marqueeContainer.addEventListener('click', function() {
			if (marquee.scrollAmount === 0) {
				marquee.scrollAmount = 8;
				marqueeContainer.style.opacity = '1';
			} else {
				marquee.scrollAmount = 0;
				marqueeContainer.style.opacity = '0.8';
			}
		});

		// Add keyboard controls for presentation
		document.addEventListener('keydown', function(e) {
			if (e.key === 'p' || e.key === 'P') {
				// Toggle pause with 'P' key
				if (marquee.scrollAmount === 0) {
					marquee.scrollAmount = 8;
				} else {
					marquee.scrollAmount = 0;
				}
			}
		});
	}

	// Add presentation tips
	setTimeout(() => {
		const tips = document.createElement('div');
		tips.innerHTML = `
			<div style="position: fixed; top: 50%; left: 20px; z-index: 999;
						background: rgba(45, 90, 39, 0.9); color: white;
						padding: 15px; border-radius: 10px; font-size: 0.8rem;
						backdrop-filter: blur(10px); max-width: 200px;
						animation: tipsFadeIn 1s ease-in-out;">
				<strong>💡 Presentation Tips:</strong><br>
				• Click marquee to pause/resume<br>
				• Press 'P' to toggle marquee<br>
				• Hover over elements for effects
			</div>
		`;
		document.body.appendChild(tips);

		// Auto-hide tips after 8 seconds
		setTimeout(() => {
			tips.style.opacity = '0';
			tips.style.transform = 'translateX(-100%)';
			setTimeout(() => tips.remove(), 500);
		}, 8000);
	}, 3000);
});

// Add CSS animations for presentation elements
const style = document.createElement('style');
style.textContent = `
	@keyframes presentationPulse {
		0% { transform: scale(1); opacity: 0.9; }
		100% { transform: scale(1.05); opacity: 1; }
	}
	@keyframes tipsFadeIn {
		0% { opacity: 0; transform: translateX(-100%); }
		100% { opacity: 1; transform: translateX(0); }
	}
`;
document.head.appendChild(style);
</script>

{% endblock %}
