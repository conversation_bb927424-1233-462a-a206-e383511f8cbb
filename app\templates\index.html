{% extends 'layout.html' %}

{% block body %}
<!-- Presentation Marquee Banner -->
<div class="presentation-marquee">
	<marquee behavior="scroll" direction="up" scrollamount="3" onmouseover="this.stop();" onmouseout="this.start();">
		<span class="presentation-marquee-text">
			� AGRICULTURE HUB - Intelligent Farming Solutions �
			AI-Powered Crop Recommendations
			AI-POWERED AGRICULTURE SOLUTIONS �
			Intelligent Soil Optimization
			Weather-Based Agricultural Insights
		</span>
	</marquee>
</div>

<!-- Presentation Hero Section -->
<section class="presentation-hero" style="background: linear-gradient(135deg, #1a4d3a 0%, #2d5a27 30%, #4a7c59 70%, #7fb069 100%); min-height: 100vh; position: relative; display: flex; align-items: center;">
	<!-- Animated Background -->
	<div class="hero-bg-animation">
		<div class="floating-particles"></div>
		<div class="grid-overlay"></div>
	</div>

	<div class="position-absolute w-100 h-100" style="background: rgba(0,0,0,0.3);"></div>

	<div class="container position-relative" style="z-index: 10;">
		<div class="row align-items-center min-vh-100">
			<div class="col-lg-6" data-aos="fade-right">
				<div class="presentation-content text-white">
					<!-- Project Badge -->
					<div class="project-badge mb-4" data-aos="fade-up">
						<span class="badge-icon">⚡</span>
						<span class="badge-text">AI-Powered Agriculture Platform</span>
					</div>

					<!-- Presentation Badge -->
					<div class="presentation-badge mb-4" data-aos="fade-up" data-aos-delay="100">
						<span class="presentation-icon">🎯</span>
						<span class="presentation-text">LIVE DEMONSTRATION</span>
						<span class="presentation-pulse"></span>
					</div>



					<div class="hero-logo-section mb-4" data-aos="fade-up" data-aos-delay="200">
						<img src="{{ url_for('static', filename='images/logo.png') }}" alt="AGRI HUB Logo" style="height: 80px; width: auto; margin-bottom: 20px; filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));">
					</div>

					<h1 class="presentation-title mb-4" data-aos="fade-up" data-aos-delay="300">
						<span class="title-line-1">Welcome to</span>
						<span class="title-line-2">
							<span class="agri-text">AGRICULTURE</span>
							<span class="hub-text">HUB</span>
						</span>
					</h1>

					<h2 class="presentation-subtitle mb-4" data-aos="fade-up" data-aos-delay="500">
						� Revolutionizing Agriculture with Artificial Intelligence
					</h2>

					<p class="presentation-description mb-5" data-aos="fade-up" data-aos-delay="700">
						Experience the future of farming with our comprehensive AI platform featuring intelligent crop recommendations,
						instant disease detection, soil optimization, weather forecasting, and data-driven agricultural insights.
					</p>

					<!-- Key Stats -->
					<div class="presentation-stats mb-5" data-aos="fade-up" data-aos-delay="900">
						<div class="stat-item">
							<div class="stat-number">99.5%</div>
							<div class="stat-label">AI Accuracy</div>
							<div class="stat-icon"><i class="fas fa-brain"></i></div>
						</div>
						<div class="stat-item">
							<div class="stat-number">38+</div>
							<div class="stat-label">Disease Types</div>
							<div class="stat-icon"><i class="fas fa-microscope"></i></div>
						</div>
						<div class="stat-item">
							<div class="stat-number">6+</div>
							<div class="stat-label">AI Features</div>
							<div class="stat-icon"><i class="fas fa-robot"></i></div>
						</div>
						<div class="stat-item">
							<div class="stat-number">24/7</div>
							<div class="stat-label">AI Support</div>
							<div class="stat-icon"><i class="fas fa-headset"></i></div>
						</div>
					</div>

					<div class="presentation-buttons" data-aos="fade-up" data-aos-delay="1100">
						<a href="#features" class="btn btn-presentation-primary btn-lg me-3">
							<i class="fas fa-rocket me-2"></i>Explore Platform
						</a>
						<a href="#demo" class="btn btn-presentation-secondary btn-lg">
							<i class="fas fa-play me-2"></i>Watch Demo
						</a>
					</div>
				</div>
			</div>

			<div class="col-lg-6" data-aos="fade-left" data-aos-delay="400">
				<div class="presentation-visual">
					<!-- Dashboard Mockup -->
					<div class="dashboard-mockup" data-aos="zoom-in" data-aos-delay="600">
						<div class="dashboard-container">
							<div class="dashboard-header">
								<div class="dashboard-title">
									<img src="{{ url_for('static', filename='images/logo.png') }}" alt="AGRI HUB Logo" style="height: 20px; width: auto; margin-right: 8px;">
									<i class="fas fa-leaf me-2"></i>AGRI HUB Dashboard
								</div>
								<div class="dashboard-status">
									<span class="status-dot"></span>Live Data
								</div>
							</div>

							<div class="dashboard-content">
								<div class="dashboard-cards">
									<div class="dashboard-card">
										<div class="card-icon">🌾</div>
										<div class="card-value">Wheat</div>
										<div class="card-label">AI Recommended</div>
									</div>
									<div class="dashboard-card">
										<div class="card-icon">🔬</div>
										<div class="card-value">99.5%</div>
										<div class="card-label">Health Score</div>
									</div>
									<div class="dashboard-card">
										<div class="card-icon">💧</div>
										<div class="card-value">NPK 20:10:10</div>
										<div class="card-label">Smart Fertilizer</div>
									</div>
									<div class="dashboard-card">
										<div class="card-icon">🌤️</div>
										<div class="card-value">Optimal</div>
										<div class="card-label">Weather AI</div>
									</div>
								</div>

								<div class="dashboard-chart">
									<div class="chart-header">AI Performance Analytics</div>
									<div class="chart-bars">
										<div class="chart-bar" style="height: 50%;">
											<span class="bar-label">Traditional</span>
										</div>
										<div class="chart-bar active" style="height: 95%;">
											<span class="bar-label">AI-Powered</span>
										</div>
									</div>
									<div class="chart-improvement">+90% Efficiency Boost</div>
								</div>
							</div>
						</div>
					</div>

					<!-- Technology Showcase -->
					<div class="tech-showcase mt-4" data-aos="fade-up" data-aos-delay="800">
						<div class="tech-items">
							<div class="tech-item">
								<i class="fab fa-python"></i>
								<span>Python</span>
							</div>
							<div class="tech-item">
								<i class="fas fa-brain"></i>
								<span>Machine Learning</span>
							</div>
							<div class="tech-item">
								<i class="fas fa-eye"></i>
								<span>Computer Vision</span>
							</div>
							<div class="tech-item">
								<i class="fas fa-cloud"></i>
								<span>Cloud AI</span>
							</div>
						</div>
					</div>
				</div>
			</div>
				<div class="hero-visual position-relative">
					<!-- Main Dashboard Mockup -->
					<div class="dashboard-mockup" data-aos="zoom-in" data-aos-delay="400">
						<div class="dashboard-header">
							<div class="dashboard-title">
								<img src="{{ url_for('static', filename='images/logo.png') }}" alt="AGRICULTURE HUB Logo" style="height: 20px; width: auto; margin-right: 8px;">
								<i class="fas fa-leaf me-2"></i>AGRICULTURE HUB Dashboard
							</div>
							<div class="dashboard-status">
								<span class="status-dot"></span>Live System
							</div>
						</div>
						<div class="dashboard-body">
							<div class="metric-row">
								<div class="metric-card">
									<div class="metric-icon">
										<i class="fas fa-seedling text-success"></i>
									</div>
									<div class="metric-info">
										<div class="metric-value">1,247</div>
										<div class="metric-label">Crops Analyzed</div>
									</div>
								</div>
								<div class="metric-card">
									<div class="metric-icon">
										<i class="fas fa-bug text-danger"></i>
									</div>
									<div class="metric-info">
										<div class="metric-value">98.5%</div>
										<div class="metric-label">Disease Detection</div>
									</div>
								</div>
							</div>
							<div class="chart-area">
								<div class="chart-title">Yield Improvement</div>
								<div class="chart-bars">
									<div class="bar" style="height: 60%;"></div>
									<div class="bar" style="height: 80%;"></div>
									<div class="bar" style="height: 95%;"></div>
									<div class="bar" style="height: 75%;"></div>
									<div class="bar" style="height: 90%;"></div>
								</div>
							</div>
						</div>
					</div>
					
					<!-- Floating Feature Cards -->
					<div class="floating-feature top-right" data-aos="fade-up" data-aos-delay="600">
						<div class="feature-icon">
							<i class="fas fa-brain text-primary"></i>
						</div>
						<div class="feature-content">
							<div class="feature-title">AI Analysis</div>
							<div class="feature-desc">Real-time Processing</div>
						</div>
					</div>
					
					<div class="floating-feature bottom-left" data-aos="fade-up" data-aos-delay="800">
						<div class="feature-icon">
							<i class="fas fa-cloud-sun text-warning"></i>
						</div>
						<div class="feature-content">
							<div class="feature-title">Weather AI</div>
							<div class="feature-desc">7-Day Forecasting</div>
						</div>
					</div>
					
					<div class="floating-feature middle-right" data-aos="fade-up" data-aos-delay="1000">
						<div class="feature-icon">
							<i class="fas fa-microscope text-info"></i>
						</div>
						<div class="feature-content">
							<div class="feature-title">Disease Detection</div>
							<div class="feature-desc">Instant Analysis</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>

<!-- Platform Features Section -->
<section id="features" class="presentation-features" style="background: linear-gradient(45deg, #f8f9fa 0%, #e9ecef 100%); padding: 80px 0;">
	<div class="container">
		<div class="text-center mb-5" data-aos="fade-up">
			<h2 class="presentation-section-title">
				🌟 Comprehensive AI-Powered Platform Features
			</h2>
			<p class="presentation-section-desc">
				Advanced agricultural technology solutions for modern cultivation
			</p>
		</div>
		
		<div class="row g-4">
			<div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="100">
				<div class="presentation-feature-card">
					<div class="presentation-feature-icon">
						<i class="fas fa-seedling"></i>
					</div>
					<h4 class="presentation-feature-title">Smart Crop Recommendation</h4>
					<p class="presentation-feature-desc">
						AI-powered crop suggestions based on soil conditions, weather patterns, and regional data for optimal yield and profitability.
					</p>
					<div class="feature-tech">
						<span class="tech-badge">Machine Learning</span>
						<span class="tech-badge">99.5% Accuracy</span>
					</div>
					<a href="{{ url_for('crop_recommend') }}" class="presentation-feature-btn">
						<i class="fas fa-arrow-right me-2"></i>Try Now
					</a>
				</div>
			</div>

			<div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="200">
				<div class="presentation-feature-card">
					<div class="presentation-feature-icon">
						<i class="fas fa-microscope"></i>
					</div>
					<h4 class="presentation-feature-title">AI Disease Detection</h4>
					<p class="presentation-feature-desc">
						Instant plant disease identification using advanced computer vision and deep learning algorithms with 38+ disease types.
					</p>
					<div class="feature-tech">
						<span class="tech-badge">Computer Vision</span>
						<span class="tech-badge">38+ Diseases</span>
					</div>
					<a href="{{ url_for('disease_prediction') }}" class="presentation-feature-btn">
						<i class="fas fa-arrow-right me-2"></i>Analyze Now
					</a>
				</div>
			</div>

			<div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="300">
				<div class="presentation-feature-card">
					<div class="presentation-feature-icon">
						<i class="fas fa-flask"></i>
					</div>
					<h4 class="presentation-feature-title">Soil Optimization</h4>
					<p class="presentation-feature-desc">
						Intelligent fertilizer recommendations to optimize soil health and maximize crop productivity with precise NPK analysis.
					</p>
					<div class="feature-tech">
						<span class="tech-badge">Soil Analysis</span>
						<span class="tech-badge">NPK Optimization</span>
					</div>
					<a href="{{ url_for('fertilizer_recommendation') }}" class="presentation-feature-btn">
						<i class="fas fa-arrow-right me-2"></i>Optimize
					</a>
				</div>
			</div>

			<div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="400">
				<div class="presentation-feature-card">
					<div class="presentation-feature-icon">
						<i class="fas fa-cloud-sun"></i>
					</div>
					<h4 class="presentation-feature-title">Weather Intelligence</h4>
					<p class="presentation-feature-desc">
						Advanced weather forecasting with agricultural timing recommendations for optimal farming decisions and risk management.
					</p>
					<div class="feature-tech">
						<span class="tech-badge">Weather API</span>
						<span class="tech-badge">7-Day Forecast</span>
					</div>
					<a href="{{ url_for('weather_recommendations') }}" class="presentation-feature-btn">
						<i class="fas fa-arrow-right me-2"></i>Check Weather
					</a>
				</div>
			</div>

			<div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="500">
				<div class="presentation-feature-card">
					<div class="presentation-feature-icon">
						<i class="fas fa-chart-line"></i>
					</div>
					<h4 class="presentation-feature-title">Crop Comparison Dashboard</h4>
					<p class="presentation-feature-desc">
						Data-driven crop comparison with profit analysis, ROI calculations, and comprehensive market insights for informed decisions.
					</p>
					<div class="feature-tech">
						<span class="tech-badge">Data Analytics</span>
						<span class="tech-badge">ROI Analysis</span>
					</div>
					<a href="{{ url_for('crop_comparison') }}" class="presentation-feature-btn">
						<i class="fas fa-arrow-right me-2"></i>Compare
					</a>
				</div>
			</div>


		</div>
	</div>
</section>

<!-- Technology Stack Section -->
<section class="tech-section" style="background: linear-gradient(135deg, #1a4d3a 0%, #2d5a27 100%); padding: 80px 0;">
	<div class="container">
		<div class="text-center mb-5" data-aos="fade-up">
			<h2 class="display-4 fw-bold text-white mb-3">
				⚡ Powered by Advanced Technology
			</h2>
			<p class="lead text-light opacity-75">
				Built with cutting-edge AI and machine learning technologies
			</p>
		</div>
		
		<div class="row g-4 text-center">
			<div class="col-lg-3 col-md-6" data-aos="zoom-in" data-aos-delay="100">
				<div class="tech-item">
					<div class="tech-icon">
						<i class="fab fa-python"></i>
					</div>
					<h5 class="tech-title">Python & Flask</h5>
					<p class="tech-desc">Robust backend framework</p>
				</div>
			</div>
			
			<div class="col-lg-3 col-md-6" data-aos="zoom-in" data-aos-delay="200">
				<div class="tech-item">
					<div class="tech-icon">
						<i class="fas fa-brain"></i>
					</div>
					<h5 class="tech-title">PyTorch</h5>
					<p class="tech-desc">Deep learning models</p>
				</div>
			</div>
			
			<div class="col-lg-3 col-md-6" data-aos="zoom-in" data-aos-delay="300">
				<div class="tech-item">
					<div class="tech-icon">
						<i class="fab fa-google"></i>
					</div>
					<h5 class="tech-title">Google AI</h5>
					<p class="tech-desc">Gemini API integration</p>
				</div>
			</div>
			
			<div class="col-lg-3 col-md-6" data-aos="zoom-in" data-aos-delay="400">
				<div class="tech-item">
					<div class="tech-icon">
						<i class="fas fa-chart-bar"></i>
					</div>
					<h5 class="tech-title">Machine Learning</h5>
					<p class="tech-desc">Advanced algorithms</p>
				</div>
			</div>
		</div>
	</div>
</section>



<style>
/* Professional Design Styles */
.professional-marquee {
	position: fixed;
	top: 80px;
	right: 20px;
	width: 300px;
	height: 200px;
	z-index: 999;
	background: linear-gradient(135deg, #1a4d3a, #4a7c59);
	box-shadow: 0 8px 25px rgba(26, 77, 58, 0.4);
	border-radius: 15px;
	border: 2px solid #7fb069;
	overflow: hidden;
	backdrop-filter: blur(10px);
}

.professional-marquee marquee {
	background: transparent;
	color: white;
	font-family: 'Inter', sans-serif;
	font-weight: 700;
	font-size: 0.9rem;
	padding: 10px;
	text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
	line-height: 1.4;
	height: 100%;
}

.marquee-content-vertical {
	display: flex;
	flex-direction: column;
	gap: 15px;
	height: 100%;
}

.marquee-item {
	display: block;
	padding: 8px 12px;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 8px;
	border-left: 3px solid #ffd700;
	font-weight: 600;
	text-align: left;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.presentation-hero {
	position: relative;
	overflow: hidden;
}

.hero-bg-animation {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 1;
}

.floating-particles {
	position: absolute;
	width: 100%;
	height: 100%;
	background-image:
		radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 2px, transparent 2px),
		radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 2px, transparent 2px);
	background-size: 100px 100px;
	animation: float 20s ease-in-out infinite;
}

.grid-overlay {
	position: absolute;
	width: 100%;
	height: 100%;
	background-image:
		linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
		linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
	background-size: 50px 50px;
	opacity: 0.3;
}

@keyframes float {
	0%, 100% { transform: translateY(0px) rotate(0deg); }
	50% { transform: translateY(-20px) rotate(180deg); }
}

.project-badge {
	background: rgba(255, 215, 0, 0.15);
	border: 1px solid rgba(255, 215, 0, 0.3);
	border-radius: 50px;
	padding: 12px 24px;
	display: inline-block;
	backdrop-filter: blur(10px);
	box-shadow: 0 4px 15px rgba(255, 215, 0, 0.2);
}

.badge-icon {
	font-size: 1.2rem;
	margin-right: 8px;
}

.badge-text {
	font-weight: 600;
	font-size: 0.9rem;
	color: #ffd700;
}

.presentation-badge {
	background: linear-gradient(135deg, #ff6b6b, #ee5a24);
	border: 2px solid #ffffff;
	border-radius: 50px;
	padding: 12px 24px;
	display: inline-flex;
	align-items: center;
	gap: 10px;
	position: relative;
	box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
	animation: presentationFloat 2s ease-in-out infinite alternate;
}

.presentation-icon {
	font-size: 1.2rem;
	animation: presentationSpin 3s linear infinite;
}

.presentation-text {
	font-weight: 800;
	font-size: 0.9rem;
	color: white;
	text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
	letter-spacing: 1px;
}

.presentation-pulse {
	position: absolute;
	top: -2px;
	left: -2px;
	right: -2px;
	bottom: -2px;
	border: 2px solid rgba(255, 255, 255, 0.6);
	border-radius: 50px;
	animation: presentationPulse 2s ease-in-out infinite;
}

@keyframes presentationFloat {
	0% {
		transform: translateY(0px);
		box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
	}
	100% {
		transform: translateY(-3px);
		box-shadow: 0 12px 35px rgba(255, 107, 107, 0.6);
	}
}

@keyframes presentationSpin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

@keyframes presentationPulse {
	0% {
		transform: scale(1);
		opacity: 1;
	}
	100% {
		transform: scale(1.05);
		opacity: 0;
	}
}

.academic-badge {
	background: rgba(255, 255, 255, 0.1);
	border: 2px solid #ffd700;
	border-radius: 15px;
	padding: 15px 25px;
	display: inline-block;
	backdrop-filter: blur(10px);
	box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
	text-align: center;
}

.academic-icon {
	font-size: 1.5rem;
	margin-right: 10px;
}

.academic-text {
	font-weight: 800;
	font-size: 1.1rem;
	color: #ffd700;
	display: block;
	margin-bottom: 5px;
}

.academic-subtitle {
	font-weight: 600;
	font-size: 0.9rem;
	color: rgba(255, 255, 255, 0.9);
	display: block;
}

/* Farmer-Friendly Design Styles */
.farmer-hero {
	position: relative;
	overflow: hidden;
	padding-top: 140px !important;
	padding-bottom: 80px !important;
}

.farm-pattern {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-image:
		radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 2px, transparent 2px),
		radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 2px, transparent 2px);
	background-size: 50px 50px;
	opacity: 0.3;
}

.welcome-badge {
	background: rgba(255, 255, 255, 0.9);
	border-radius: 50px;
	padding: 12px 24px;
	display: inline-flex;
	align-items: center;
	gap: 10px;
	box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.welcome-icon {
	font-size: 1.5rem;
}

.welcome-text {
	font-weight: 700;
	color: #2d5a27;
	font-size: 1.1rem;
}

.farmer-title {
	text-align: left;
}

.presentation-title {
	text-align: left;
}

.title-line-1 {
	display: block;
	font-size: 2rem;
	font-weight: 600;
	color: rgba(255, 255, 255, 0.9);
	margin-bottom: 10px;
}

.title-line-2 {
	display: block;
	background: linear-gradient(45deg, #ffd700, #ffed4e, #fff59d);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
	text-shadow: 0 0 30px rgba(255, 215, 0, 0.3);
}

.agri-text, .hub-text {
	font-size: 4rem;
	font-weight: 900;
	line-height: 1;
	margin-right: 15px;
}

.agri-text {
	color: #ffd700;
}

.hub-text {
	color: #fff59d;
}

.presentation-subtitle {
	font-size: 1.5rem;
	font-weight: 600;
	color: #ffd700;
}

.presentation-description {
	font-size: 1.2rem;
	line-height: 1.7;
	color: rgba(255, 255, 255, 0.9);
}

.presentation-stats {
	display: flex;
	gap: 20px;
	flex-wrap: wrap;
}

.stat-item {
	text-align: center;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 15px;
	padding: 20px 15px;
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.2);
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
	min-width: 120px;
}

.stat-item:hover {
	transform: translateY(-5px);
	background: rgba(255, 255, 255, 0.15);
	box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
}

.stat-item::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 3px;
	background: linear-gradient(90deg, #ffd700, #ffed4e);
	border-radius: 15px 15px 0 0;
}

.stat-number {
	font-size: 2.2rem;
	font-weight: 900;
	color: #ffd700;
	line-height: 1;
	text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
	font-size: 0.9rem;
	opacity: 0.9;
	margin-top: 8px;
	font-weight: 600;
}

.stat-icon {
	margin-top: 10px;
	font-size: 1.2rem;
	color: #ffed4e;
	opacity: 0.8;
}

.btn-presentation-primary {
	background: linear-gradient(45deg, #ffd700, #ffed4e);
	border: none;
	color: #1a4d3a;
	font-weight: 700;
	padding: 15px 30px;
	border-radius: 50px;
	transition: all 0.3s ease;
	box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.btn-presentation-primary:hover {
	transform: translateY(-2px);
	box-shadow: 0 10px 25px rgba(255, 215, 0, 0.5);
	color: #1a4d3a;
	background: linear-gradient(45deg, #ffed4e, #fff59d);
}

.btn-presentation-secondary {
	background: transparent;
	border: 2px solid #ffd700;
	color: #ffd700;
	font-weight: 700;
	padding: 13px 28px;
	border-radius: 50px;
	transition: all 0.3s ease;
}

.btn-presentation-secondary:hover {
	background: #ffd700;
	color: #1a4d3a;
	transform: translateY(-2px);
	box-shadow: 0 10px 25px rgba(255, 215, 0, 0.3);
}

.farmer-description {
	font-size: 1.2rem;
	line-height: 1.8;
	color: #2d5a27;
	font-weight: 500;
}

.farmer-actions {
	display: flex;
	gap: 20px;
	flex-wrap: wrap;
}

.farmer-btn {
	display: flex;
	align-items: center;
	gap: 12px;
	padding: 18px 30px;
	border-radius: 15px;
	text-decoration: none;
	font-weight: 700;
	font-size: 1.1rem;
	transition: all 0.3s ease;
	box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.farmer-btn.primary {
	background: linear-gradient(135deg, #4a7c59, #7fb069);
	color: white;
}

.farmer-btn.secondary {
	background: linear-gradient(135deg, #87ceeb, #98fb98);
	color: #2d5a27;
}

.farmer-btn:hover {
	transform: translateY(-3px);
	box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
	color: inherit;
}

.farmer-btn i {
	font-size: 1.3rem;
}

.trust-indicators {
	display: flex;
	gap: 30px;
	flex-wrap: wrap;
}

.trust-item {
	text-align: center;
	background: rgba(255, 255, 255, 0.8);
	padding: 20px;
	border-radius: 15px;
	box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.trust-number {
	font-size: 2rem;
	font-weight: 900;
	color: #2d5a27;
	line-height: 1;
}

.trust-label {
	font-size: 0.9rem;
	color: #4a7c59;
	font-weight: 600;
	margin-top: 5px;
}

/* Moving Marquee Banner */
.marquee-banner {
	position: fixed;
	top: 80px;
	left: 0;
	width: 100%;
	z-index: 999;
	background: linear-gradient(135deg, #4a7c59, #7fb069);
	box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
	border-bottom: 3px solid #2d5a27;
	overflow: hidden;
}

.marquee-banner marquee {
	background: transparent;
	color: white;
	font-family: 'Inter', sans-serif;
	font-weight: 700;
	font-size: 1.1rem;
	padding: 12px 0;
	text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
	line-height: 1.2;
}

.marquee-text {
	display: inline-block;
	white-space: nowrap;
	letter-spacing: 1px;
}

.marquee-close {
	position: absolute;
	top: 50%;
	right: 15px;
	transform: translateY(-50%);
	background: #2d5a27;
	color: white;
	border: none;
	border-radius: 50%;
	width: 30px;
	height: 30px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	font-size: 0.8rem;
	transition: all 0.3s ease;
	box-shadow: 0 2px 8px rgba(45, 90, 39, 0.3);
}

.marquee-close:hover {
	background: #1a4d3a;
	transform: translateY(-50%) scale(1.1);
	box-shadow: 0 4px 12px rgba(26, 77, 58, 0.5);
}

.marquee-content {
	display: flex;
	animation: marqueeScroll 25s linear infinite;
	white-space: nowrap;
}

.marquee-text {
	font-size: 1.1rem;
	font-weight: 700;
	color: #1a4d3a;
	padding: 12px 0;
	text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.9);
	display: inline-block;
	min-width: 100%;
}

@keyframes marqueeScroll {
	0% {
		transform: translateX(100%);
	}
	100% {
		transform: translateX(-100%);
	}
}

@keyframes marqueeFloat {
	0% {
		transform: translateY(0px);
		box-shadow: 0 4px 15px rgba(127, 176, 105, 0.4);
	}
	100% {
		transform: translateY(-3px);
		box-shadow: 0 8px 25px rgba(127, 176, 105, 0.6);
	}
}



/* Mobile-First Responsive Design */
@media (max-width: 768px) {
	.marquee-banner {
		top: 70px;
	}

	.marquee-banner marquee {
		font-size: 1rem;
		padding: 10px 0;
	}

	.farmer-hero {
		padding-top: 120px !important;
		padding-bottom: 60px !important;
	}

	.main-title {
		font-size: 2.8rem;
		text-align: center;
	}

	.sub-title {
		font-size: 1.3rem;
		text-align: center;
	}

	.farmer-description {
		font-size: 1.1rem;
		text-align: center;
		padding: 0 15px;
	}

	.farmer-actions {
		flex-direction: column;
		align-items: center;
		gap: 15px;
	}

	.farmer-btn {
		width: 100%;
		max-width: 280px;
		justify-content: center;
		text-align: center;
		padding: 16px 20px;
	}

	.trust-indicators {
		justify-content: center;
		gap: 15px;
	}

	.trust-item {
		flex: 1;
		min-width: 100px;
	}

	.feature-grid {
		grid-template-columns: 1fr;
		gap: 15px;
	}

	.farmer-feature-card {
		padding: 25px 20px;
	}

	.farmer-section-title {
		font-size: 2rem;
		text-align: center;
	}

	.farmer-section-desc {
		font-size: 1.1rem;
		text-align: center;
	}
}

@media (max-width: 480px) {
	.marquee-banner {
		top: 65px;
	}

	.marquee-banner marquee {
		font-size: 0.9rem;
		padding: 8px 0;
	}

	.farmer-hero {
		padding-top: 100px !important;
		padding-bottom: 40px !important;
	}

	.welcome-badge {
		padding: 10px 20px;
	}

	.welcome-text {
		font-size: 1rem;
	}

	.main-title {
		font-size: 2.2rem;
	}

	.sub-title {
		font-size: 1.1rem;
	}

	.farmer-description {
		font-size: 1rem;
		padding: 0 10px;
		line-height: 1.6;
	}

	.farmer-btn {
		padding: 14px 18px;
		font-size: 0.95rem;
		width: 100%;
		max-width: 250px;
	}

	.farmer-btn i {
		font-size: 1.1rem;
	}

	.trust-item {
		padding: 12px;
		min-width: 80px;
	}

	.trust-number {
		font-size: 1.3rem;
	}

	.trust-label {
		font-size: 0.8rem;
	}

	.farmer-feature-card {
		padding: 20px 15px;
	}

	.farmer-feature-icon {
		font-size: 2.5rem;
		margin-bottom: 15px;
	}

	.farmer-feature-title {
		font-size: 1rem;
		margin-bottom: 12px;
	}

	.farmer-feature-desc {
		font-size: 0.9rem;
		margin-bottom: 15px;
	}

	.farmer-feature-btn {
		padding: 10px 20px;
		font-size: 0.9rem;
	}

	.farmer-section-title {
		font-size: 1.8rem;
	}

	.farmer-section-desc {
		font-size: 1rem;
	}

	.feature-card-simple {
		padding: 20px 15px;
	}

	.feature-icon-simple {
		font-size: 2.5rem;
		margin-bottom: 12px;
	}

	.farmer-testimonial {
		padding: 20px 15px;
	}

	.testimonial-content p {
		font-size: 1rem;
	}
}

/* Extra small screens */
@media (max-width: 360px) {
	.marquee-banner marquee {
		font-size: 0.8rem;
		padding: 6px 0;
	}

	.main-title {
		font-size: 2rem;
	}

	.sub-title {
		font-size: 1rem;
	}

	.farmer-description {
		font-size: 0.95rem;
	}

	.farmer-btn {
		font-size: 0.9rem;
		padding: 12px 16px;
	}

	.trust-indicators {
		gap: 10px;
	}

	.trust-item {
		padding: 10px;
	}

	.farmer-feature-icon {
		font-size: 2rem;
	}

	.farmer-section-title {
		font-size: 1.6rem;
	}
}

/* Dashboard Mockup Styles */
.dashboard-mockup {
	perspective: 1000px;
}

.dashboard-container {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20px;
	padding: 25px;
	box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
	backdrop-filter: blur(15px);
	border: 1px solid rgba(255, 255, 255, 0.2);
	transform: rotateY(-5deg) rotateX(5deg);
	transition: all 0.3s ease;
}

.dashboard-container:hover {
	transform: rotateY(0deg) rotateX(0deg) scale(1.02);
	box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4);
}

.dashboard-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;
	padding-bottom: 15px;
	border-bottom: 2px solid #e9ecef;
}

.dashboard-title {
	font-size: 1.2rem;
	font-weight: 700;
	color: #1a4d3a;
	display: flex;
	align-items: center;
}

.dashboard-status {
	display: flex;
	align-items: center;
	gap: 8px;
	font-size: 0.9rem;
	color: #28a745;
	font-weight: 600;
}

.status-dot {
	width: 8px;
	height: 8px;
	background: #28a745;
	border-radius: 50%;
	animation: pulse 2s infinite;
}

@keyframes pulse {
	0% { opacity: 1; }
	50% { opacity: 0.5; }
	100% { opacity: 1; }
}

.dashboard-cards {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 15px;
	margin-bottom: 25px;
}

.dashboard-card {
	background: linear-gradient(135deg, #f8f9fa, #e9ecef);
	padding: 20px;
	border-radius: 12px;
	text-align: center;
	border: 1px solid #dee2e6;
	transition: all 0.3s ease;
}

.dashboard-card:hover {
	transform: translateY(-3px);
	box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.card-icon {
	font-size: 2rem;
	margin-bottom: 10px;
}

.card-value {
	font-size: 1.1rem;
	font-weight: 700;
	color: #1a4d3a;
	margin-bottom: 5px;
}

.card-label {
	font-size: 0.8rem;
	color: #6c757d;
	font-weight: 500;
}

.dashboard-chart {
	background: linear-gradient(135deg, #1a4d3a, #2d5a27);
	padding: 20px;
	border-radius: 12px;
	color: white;
	text-align: center;
}

.chart-header {
	font-size: 1rem;
	font-weight: 600;
	margin-bottom: 15px;
	color: #ffd700;
}

.chart-bars {
	display: flex;
	justify-content: center;
	align-items: end;
	gap: 20px;
	height: 80px;
	margin-bottom: 15px;
}

.chart-bar {
	width: 40px;
	background: linear-gradient(to top, #4a7c59, #7fb069);
	border-radius: 4px 4px 0 0;
	position: relative;
	transition: all 0.3s ease;
}

.chart-bar.active {
	background: linear-gradient(to top, #ffd700, #ffed4e);
	box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.bar-label {
	position: absolute;
	bottom: -25px;
	left: 50%;
	transform: translateX(-50%);
	font-size: 0.8rem;
	font-weight: 600;
	white-space: nowrap;
}

.chart-improvement {
	font-size: 1.1rem;
	font-weight: 700;
	color: #ffd700;
	text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.tech-showcase {
	background: rgba(255, 255, 255, 0.1);
	border-radius: 15px;
	padding: 20px;
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.2);
}

.tech-items {
	display: flex;
	justify-content: space-around;
	flex-wrap: wrap;
	gap: 15px;
}

.tech-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8px;
	color: white;
	font-size: 0.9rem;
	font-weight: 600;
}

.tech-item i {
	font-size: 2rem;
	color: #ffd700;
}

/* Farmer Visual Elements */
.feature-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 20px;
	margin-bottom: 30px;
}

.feature-card-simple {
	background: rgba(255, 255, 255, 0.9);
	padding: 25px;
	border-radius: 15px;
	text-align: center;
	box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
}

.feature-card-simple:hover {
	transform: translateY(-5px);
	box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.feature-icon-simple {
	font-size: 3rem;
	margin-bottom: 15px;
}

.feature-card-simple h4 {
	color: #2d5a27;
	font-weight: 700;
	margin-bottom: 10px;
	font-size: 1.1rem;
}

.feature-card-simple p {
	color: #4a7c59;
	font-size: 0.9rem;
	margin: 0;
}

.farmer-testimonial {
	background: rgba(255, 255, 255, 0.9);
	padding: 25px;
	border-radius: 15px;
	box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
	border-left: 5px solid #4a7c59;
}

.testimonial-content p {
	font-style: italic;
	color: #2d5a27;
	font-size: 1.1rem;
	margin-bottom: 15px;
}

.farmer-info {
	color: #4a7c59;
	font-weight: 600;
}

/* Presentation Feature Cards */
.presentation-section-title {
	font-size: 2.8rem;
	font-weight: 800;
	color: #1a4d3a;
	margin-bottom: 15px;
}

.presentation-section-desc {
	font-size: 1.3rem;
	color: #4a7c59;
	font-weight: 500;
}

.presentation-feature-card {
	background: white;
	padding: 35px;
	border-radius: 20px;
	text-align: center;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
	border: 2px solid transparent;
	height: 100%;
	position: relative;
	overflow: hidden;
}

.presentation-feature-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 4px;
	background: linear-gradient(90deg, #1a4d3a, #4a7c59, #7fb069);
}

.presentation-feature-card:hover {
	transform: translateY(-15px);
	box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
	border-color: #7fb069;
}

.presentation-feature-icon {
	width: 80px;
	height: 80px;
	background: linear-gradient(135deg, #1a4d3a, #4a7c59);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 25px auto;
	transition: all 0.3s ease;
}

.presentation-feature-icon i {
	font-size: 2.5rem;
	color: white;
}

.presentation-feature-card:hover .presentation-feature-icon {
	transform: scale(1.1) rotate(5deg);
	background: linear-gradient(135deg, #4a7c59, #7fb069);
}

.presentation-feature-title {
	color: #1a4d3a;
	font-weight: 700;
	margin-bottom: 20px;
	font-size: 1.4rem;
}

.presentation-feature-desc {
	color: #4a7c59;
	font-size: 1rem;
	line-height: 1.7;
	margin-bottom: 25px;
}

.feature-tech {
	display: flex;
	justify-content: center;
	gap: 10px;
	margin-bottom: 25px;
	flex-wrap: wrap;
}

.tech-badge {
	background: linear-gradient(135deg, #e9ecef, #f8f9fa);
	color: #1a4d3a;
	padding: 6px 12px;
	border-radius: 20px;
	font-size: 0.8rem;
	font-weight: 600;
	border: 1px solid #dee2e6;
}

.presentation-feature-btn {
	background: linear-gradient(135deg, #1a4d3a, #4a7c59);
	color: white;
	padding: 12px 30px;
	border-radius: 25px;
	text-decoration: none;
	font-weight: 600;
	display: inline-flex;
	align-items: center;
	transition: all 0.3s ease;
	font-size: 1rem;
}

.presentation-feature-btn:hover {
	background: linear-gradient(135deg, #4a7c59, #7fb069);
	color: white;
	transform: scale(1.05);
	box-shadow: 0 5px 15px rgba(26, 77, 58, 0.3);
}

/* Farmer-Friendly Feature Cards */
.farmer-section-title {
	font-size: 2.5rem;
	font-weight: 800;
	color: #2d5a27;
	margin-bottom: 15px;
}

.farmer-section-desc {
	font-size: 1.2rem;
	color: #4a7c59;
	font-weight: 500;
}

.farmer-feature-card {
	background: white;
	padding: 30px;
	border-radius: 20px;
	text-align: center;
	box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
	border: 3px solid transparent;
	height: 100%;
}

.farmer-feature-card:hover {
	transform: translateY(-10px);
	box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
	border-color: #7fb069;
}

.farmer-feature-icon {
	font-size: 4rem;
	margin-bottom: 20px;
	display: block;
}

.farmer-feature-title {
	color: #2d5a27;
	font-weight: 700;
	margin-bottom: 15px;
	font-size: 1.3rem;
}

.farmer-feature-desc {
	color: #4a7c59;
	font-size: 1rem;
	line-height: 1.6;
	margin-bottom: 20px;
}

.farmer-feature-btn {
	background: linear-gradient(135deg, #4a7c59, #7fb069);
	color: white;
	padding: 12px 25px;
	border-radius: 25px;
	text-decoration: none;
	font-weight: 600;
	display: inline-block;
	transition: all 0.3s ease;
}

.farmer-feature-btn:hover {
	background: linear-gradient(135deg, #2d5a27, #4a7c59);
	color: white;
	transform: scale(1.05);
}

/* Hero Section Styles */
.project-badge {
	background: rgba(255, 255, 255, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.2);
	border-radius: 50px;
	padding: 12px 24px;
	display: inline-flex;
	align-items: center;
	gap: 10px;
	backdrop-filter: blur(10px);
}

.badge-icon {
	font-size: 1.2rem;
}

.badge-text {
	font-weight: 600;
	font-size: 0.9rem;
}

.demo-badge {
	background: linear-gradient(135deg, #ff6b6b, #ee5a24);
	border: 2px solid #ffffff;
	border-radius: 50px;
	padding: 12px 24px;
	display: inline-flex;
	align-items: center;
	gap: 10px;
	position: relative;
	box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
	animation: demoBadgeFloat 2s ease-in-out infinite alternate;
}

.demo-icon {
	font-size: 1.2rem;
	animation: demoIconSpin 3s linear infinite;
}

.demo-text {
	font-weight: 800;
	font-size: 0.9rem;
	color: white;
	text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
	letter-spacing: 1px;
}

.demo-pulse {
	position: absolute;
	top: -2px;
	left: -2px;
	right: -2px;
	bottom: -2px;
	border: 2px solid rgba(255, 255, 255, 0.6);
	border-radius: 50px;
	animation: demoPulse 2s ease-in-out infinite;
}

@keyframes demoBadgeFloat {
	0% {
		transform: translateY(0px);
		box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
	}
	100% {
		transform: translateY(-3px);
		box-shadow: 0 12px 35px rgba(255, 107, 107, 0.6);
	}
}

@keyframes demoIconSpin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

@keyframes demoPulse {
	0% {
		transform: scale(1);
		opacity: 1;
	}
	100% {
		transform: scale(1.05);
		opacity: 0;
	}
}

.developer-badge {
	background: rgba(255, 215, 0, 0.15);
	border: 1px solid rgba(255, 215, 0, 0.3);
	border-radius: 50px;
	padding: 12px 24px;
	display: inline-block;
	text-align: center;
	backdrop-filter: blur(10px);
	box-shadow: 0 4px 15px rgba(255, 215, 0, 0.2);
}

.dev-icon {
	font-size: 1.2rem;
	margin-right: 8px;
}

.dev-text {
	font-weight: 700;
	font-size: 1rem;
	color: #ffd700;
	display: block;
	margin-bottom: 2px;
}

.dev-subtitle {
	font-weight: 500;
	font-size: 0.8rem;
	color: rgba(255, 255, 255, 0.8);
	display: block;
}

.hero-title {
	font-size: 4rem;
	font-weight: 900;
	line-height: 1.1;
}

.title-line-1 {
	display: block;
	font-size: 2.5rem;
	font-weight: 400;
	opacity: 0.9;
}

.title-line-2 {
	display: block;
	background: linear-gradient(45deg, #a7c957, #7fb069, #4a7c59);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
	text-shadow: 0 0 30px rgba(167, 201, 87, 0.3);
}

.agri-text {
	margin-right: 0.2em;
}

.hub-text {
	color: #7fb069 !important;
	-webkit-text-fill-color: #7fb069 !important;
}

.hero-subtitle {
	font-size: 1.5rem;
	font-weight: 600;
	color: #a7c957;
}

.hero-description {
	font-size: 1.1rem;
	line-height: 1.7;
	opacity: 0.9;
}

.hero-stats {
	display: flex;
	gap: 2rem;
	flex-wrap: wrap;
}

.stat-item {
	text-align: center;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 15px;
	padding: 20px 15px;
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.2);
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.stat-item:hover {
	transform: translateY(-5px);
	background: rgba(255, 255, 255, 0.15);
	box-shadow: 0 10px 30px rgba(167, 201, 87, 0.3);
}

.stat-item::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 3px;
	background: linear-gradient(90deg, #a7c957, #7fb069);
	border-radius: 15px 15px 0 0;
}

.stat-number {
	font-size: 2.2rem;
	font-weight: 900;
	color: #a7c957;
	line-height: 1;
	text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
	font-size: 0.9rem;
	opacity: 0.9;
	margin-top: 8px;
	font-weight: 600;
}

.stat-icon {
	margin-top: 10px;
	font-size: 1.2rem;
	color: #7fb069;
	opacity: 0.8;
}

.btn-hero-primary {
	background: linear-gradient(45deg, #a7c957, #7fb069);
	border: none;
	color: #1a4d3a;
	font-weight: 700;
	padding: 15px 30px;
	border-radius: 50px;
	transition: all 0.3s ease;
	box-shadow: 0 4px 15px rgba(167, 201, 87, 0.3);
}

.btn-hero-primary:hover {
	transform: translateY(-2px);
	box-shadow: 0 10px 25px rgba(167, 201, 87, 0.5);
	color: #1a4d3a;
	background: linear-gradient(45deg, #7fb069, #4a7c59);
}

.btn-hero-secondary {
	background: transparent;
	border: 2px solid rgba(255, 255, 255, 0.3);
	color: white;
	font-weight: 600;
	padding: 13px 28px;
	border-radius: 50px;
	transition: all 0.3s ease;
}

.btn-hero-secondary:hover {
	background: rgba(255, 255, 255, 0.1);
	border-color: rgba(255, 255, 255, 0.5);
	color: white;
	transform: translateY(-2px);
}

/* Dashboard Mockup */
.dashboard-mockup {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20px;
	padding: 20px;
	box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
	backdrop-filter: blur(10px);
}

.dashboard-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;
	padding-bottom: 15px;
	border-bottom: 1px solid #e9ecef;
}

.dashboard-title {
	font-weight: 700;
	color: #1a4d3a;
	font-size: 1.1rem;
}

.dashboard-status {
	display: flex;
	align-items: center;
	gap: 8px;
	font-size: 0.9rem;
	color: #28a745;
	font-weight: 600;
}

.status-dot {
	width: 8px;
	height: 8px;
	background: #28a745;
	border-radius: 50%;
	animation: pulse 2s infinite;
}

.metric-row {
	display: flex;
	gap: 15px;
	margin-bottom: 20px;
}

.metric-card {
	flex: 1;
	background: #f8f9fa;
	border-radius: 12px;
	padding: 15px;
	display: flex;
	align-items: center;
	gap: 12px;
}

.metric-icon i {
	font-size: 1.5rem;
}

.metric-value {
	font-size: 1.3rem;
	font-weight: 700;
	color: #1a4d3a;
	line-height: 1;
}

.metric-label {
	font-size: 0.8rem;
	color: #6c757d;
	margin-top: 2px;
}

.chart-area {
	background: #f8f9fa;
	border-radius: 12px;
	padding: 15px;
}

.chart-title {
	font-size: 0.9rem;
	font-weight: 600;
	color: #1a4d3a;
	margin-bottom: 10px;
}

.chart-bars {
	display: flex;
	align-items: end;
	gap: 8px;
	height: 60px;
}

.bar {
	flex: 1;
	background: linear-gradient(to top, #7fb069, #4a7c59);
	border-radius: 4px 4px 0 0;
	animation: growUp 2s ease-out;
}

/* Floating Features */
.floating-feature {
	position: absolute;
	background: rgba(255, 255, 255, 0.95);
	border-radius: 15px;
	padding: 15px;
	display: flex;
	align-items: center;
	gap: 12px;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
	backdrop-filter: blur(10px);
	min-width: 200px;
}

.top-right {
	top: 10%;
	right: -5%;
}

.bottom-left {
	bottom: 20%;
	left: -10%;
}

.middle-right {
	top: 50%;
	right: -8%;
}

.feature-icon {
	width: 40px;
	height: 40px;
	border-radius: 10px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #f8f9fa;
}

.feature-icon i {
	font-size: 1.2rem;
}

.feature-title {
	font-weight: 700;
	color: #1a4d3a;
	font-size: 0.9rem;
	line-height: 1;
}

.feature-desc {
	font-size: 0.8rem;
	color: #6c757d;
	margin-top: 2px;
}

/* Feature Cards */
.feature-card {
	background: white;
	border-radius: 20px;
	padding: 30px;
	text-align: center;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
	border: 1px solid #f0f0f0;
}

.feature-card:hover {
	transform: translateY(-10px);
	box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.feature-card-icon {
	width: 80px;
	height: 80px;
	margin: 0 auto 20px;
	background: linear-gradient(45deg, #7fb069, #4a7c59);
	border-radius: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.feature-card-icon i {
	font-size: 2rem;
	color: white;
}

.feature-card-title {
	font-size: 1.3rem;
	font-weight: 700;
	color: #1a4d3a;
	margin-bottom: 15px;
}

.feature-card-desc {
	color: #6c757d;
	line-height: 1.6;
	margin-bottom: 20px;
}

.feature-card-link {
	color: #7fb069;
	text-decoration: none;
	font-weight: 600;
	transition: all 0.3s ease;
}

.feature-card-link:hover {
	color: #4a7c59;
}

/* Tech Section */
.tech-item {
	color: white;
}

.tech-icon {
	width: 80px;
	height: 80px;
	margin: 0 auto 20px;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 1px solid rgba(255, 255, 255, 0.2);
}

.tech-icon i {
	font-size: 2rem;
	color: #ffd700;
}

.tech-title {
	font-weight: 700;
	margin-bottom: 10px;
}

.tech-desc {
	opacity: 0.8;
	font-size: 0.9rem;
}

/* Animations */
@keyframes pulse {
	0%, 100% { opacity: 1; }
	50% { opacity: 0.5; }
}

@keyframes growUp {
	from { height: 0; }
	to { height: inherit; }
}

@keyframes float {
	0%, 100% { transform: translateY(0px); }
	50% { transform: translateY(-10px); }
}

/* Responsive */
@media (max-width: 768px) {
	.hero-title {
		font-size: 2.5rem;
	}
	
	.title-line-1 {
		font-size: 1.8rem;
	}
	
	.hero-stats {
		gap: 1rem;
	}
	
	.floating-feature {
		position: relative;
		top: auto !important;
		right: auto !important;
		bottom: auto !important;
		left: auto !important;
		margin: 10px 0;
	}
}
</style>

<script>
// Simple functionality for farmer-friendly design
document.addEventListener('DOMContentLoaded', function() {
	// Smooth scrolling for navigation links
	const links = document.querySelectorAll('a[href^="#"]');
	links.forEach(link => {
		link.addEventListener('click', function(e) {
			e.preventDefault();
			const target = document.querySelector(this.getAttribute('href'));
			if (target) {
				target.scrollIntoView({
					behavior: 'smooth',
					block: 'start'
				});
			}
		});
	});

	// Add simple hover effects for feature cards
	const featureCards = document.querySelectorAll('.farmer-feature-card');
	featureCards.forEach(card => {
		card.addEventListener('mouseenter', function() {
			this.style.transform = 'translateY(-10px) scale(1.02)';
		});

		card.addEventListener('mouseleave', function() {
			this.style.transform = 'translateY(0) scale(1)';
		});
	});
});
</script>

{% endblock %}
