{% extends 'layout.html' %}

{% block body %}
<!-- <PERSON> Header -->
<div class="page-header">
	<div class="container">
		<h1 data-aos="fade-up">
			<i class="fas fa-cloud-sun me-3"></i>Weather-Aware Recommendations
		</h1>
		<p data-aos="fade-up" data-aos-delay="200">
			Get 7-day weather forecasts with intelligent agricultural recommendations tailored to upcoming weather conditions
		</p>
	</div>
</div>

<!-- Main Content -->
<div class="container py-5">
	<div class="row justify-content-center">
		<div class="col-lg-10">
			<!-- Info Cards -->
			<div class="row mb-5">
				<div class="col-md-4 mb-3" data-aos="fade-up" data-aos-delay="100">
					<div class="card-modern text-center p-3">
						<i class="fas fa-calendar-week text-primary fa-2x mb-2"></i>
						<h6 class="fw-bold">7-Day Forecast</h6>
						<small class="text-muted">Detailed weather predictions</small>
					</div>
				</div>
				<div class="col-md-4 mb-3" data-aos="fade-up" data-aos-delay="200">
					<div class="card-modern text-center p-3">
						<i class="fas fa-lightbulb text-warning fa-2x mb-2"></i>
						<h6 class="fw-bold">Smart Recommendations</h6>
						<small class="text-muted">AI-powered farming advice</small>
					</div>
				</div>
				<div class="col-md-4 mb-3" data-aos="fade-up" data-aos-delay="300">
					<div class="card-modern text-center p-3">
						<i class="fas fa-shield-alt text-success fa-2x mb-2"></i>
						<h6 class="fw-bold">Risk Prevention</h6>
						<small class="text-muted">Protect crops from weather damage</small>
					</div>
				</div>
			</div>

			<!-- City Input -->
			<div class="form-modern mb-5" data-aos="fade-up" data-aos-delay="400">
				<div class="text-center mb-4">
					<h3 class="text-gradient fw-bold">Enter Your Location</h3>
					<p class="text-muted">Get weather forecast and farming recommendations for your area</p>
				</div>

				<div class="row justify-content-center">
					<div class="col-md-6">
						<div class="input-group">
							<input type="text" class="form-control form-control-modern" id="cityInput" 
								   placeholder="Enter city name (e.g., New Delhi, Mumbai)" 
								   onkeypress="handleKeyPress(event)">
							<button class="btn btn-modern" type="button" id="getWeatherBtn" onclick="getWeatherForecast()">
								<i class="fas fa-search me-1"></i>Get Forecast
							</button>
						</div>
					</div>
				</div>
			</div>

			<!-- Loading Indicator -->
			<div class="text-center mb-4" id="loadingIndicator" style="display: none;">
				<div class="spinner-border text-primary" role="status">
					<span class="visually-hidden">Loading...</span>
				</div>
				<p class="mt-2 text-muted">Fetching weather data and generating recommendations...</p>
			</div>

			<!-- Weather Results -->
			<div id="weatherResults" style="display: none;">
				<!-- Current Weather -->
				<div class="card-modern mb-4" id="currentWeather">
					<!-- Current weather will be populated here -->
				</div>

				<!-- 7-Day Forecast -->
				<div class="row" id="weeklyForecast">
					<!-- Weekly forecast cards will be populated here -->
				</div>
			</div>

			<!-- Error Message -->
			<div class="alert alert-danger" id="errorMessage" style="display: none;">
				<i class="fas fa-exclamation-triangle me-2"></i>
				<span id="errorText"></span>
			</div>

			<!-- How it Works -->
			<div class="mt-5" data-aos="fade-up">
				<h4 class="text-center mb-4 fw-bold">How Weather-Aware Recommendations Work</h4>
				<div class="row">
					<div class="col-md-3 text-center mb-3">
						<div class="bg-primary bg-opacity-10 rounded-circle p-3 d-inline-block mb-3">
							<i class="fas fa-map-marker-alt text-primary fa-2x"></i>
						</div>
						<h6 class="fw-bold">Location Input</h6>
						<small class="text-muted">Enter your city or farm location</small>
					</div>
					<div class="col-md-3 text-center mb-3">
						<div class="bg-info bg-opacity-10 rounded-circle p-3 d-inline-block mb-3">
							<i class="fas fa-cloud-download-alt text-info fa-2x"></i>
						</div>
						<h6 class="fw-bold">Weather Data</h6>
						<small class="text-muted">Fetch 7-day detailed forecast</small>
					</div>
					<div class="col-md-3 text-center mb-3">
						<div class="bg-warning bg-opacity-10 rounded-circle p-3 d-inline-block mb-3">
							<i class="fas fa-brain text-warning fa-2x"></i>
						</div>
						<h6 class="fw-bold">AI Analysis</h6>
						<small class="text-muted">Analyze weather patterns</small>
					</div>
					<div class="col-md-3 text-center mb-3">
						<div class="bg-success bg-opacity-10 rounded-circle p-3 d-inline-block mb-3">
							<i class="fas fa-clipboard-list text-success fa-2x"></i>
						</div>
						<h6 class="fw-bold">Recommendations</h6>
						<small class="text-muted">Get actionable farming advice</small>
					</div>
				</div>
			</div>

			<!-- Benefits -->
			<div class="mt-5" data-aos="fade-up">
				<h4 class="text-center mb-4 fw-bold">Benefits of Weather-Aware Farming</h4>
				<div class="row">
					<div class="col-md-6 mb-3">
						<div class="d-flex align-items-start">
							<div class="bg-success bg-opacity-10 rounded-circle p-2 me-3 mt-1">
								<i class="fas fa-shield-alt text-success"></i>
							</div>
							<div>
								<h6 class="fw-bold">Crop Protection</h6>
								<small class="text-muted">Protect crops from adverse weather conditions with timely warnings</small>
							</div>
						</div>
					</div>
					<div class="col-md-6 mb-3">
						<div class="d-flex align-items-start">
							<div class="bg-primary bg-opacity-10 rounded-circle p-2 me-3 mt-1">
								<i class="fas fa-tint text-primary"></i>
							</div>
							<div>
								<h6 class="fw-bold">Water Management</h6>
								<small class="text-muted">Optimize irrigation based on rainfall predictions</small>
							</div>
						</div>
					</div>
					<div class="col-md-6 mb-3">
						<div class="d-flex align-items-start">
							<div class="bg-warning bg-opacity-10 rounded-circle p-2 me-3 mt-1">
								<i class="fas fa-calendar-check text-warning"></i>
							</div>
							<div>
								<h6 class="fw-bold">Activity Planning</h6>
								<small class="text-muted">Plan farming activities based on weather conditions</small>
							</div>
						</div>
					</div>
					<div class="col-md-6 mb-3">
						<div class="d-flex align-items-start">
							<div class="bg-info bg-opacity-10 rounded-circle p-2 me-3 mt-1">
								<i class="fas fa-chart-line text-info"></i>
							</div>
							<div>
								<h6 class="fw-bold">Yield Optimization</h6>
								<small class="text-muted">Maximize crop yield with weather-informed decisions</small>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<style>
.weather-card {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border-radius: 15px;
	padding: 1.5rem;
	margin-bottom: 1rem;
	box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.forecast-card {
	background: white;
	border-radius: 15px;
	padding: 1rem;
	margin-bottom: 1rem;
	box-shadow: 0 4px 6px rgba(0,0,0,0.1);
	transition: all 0.3s ease;
	border-left: 4px solid #007bff;
}

.forecast-card:hover {
	transform: translateY(-5px);
	box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.weather-icon {
	font-size: 3rem;
	margin-bottom: 1rem;
}

.temp-display {
	font-size: 2.5rem;
	font-weight: bold;
}

.recommendation-item {
	background: #f8f9fa;
	border-left: 3px solid #28a745;
	padding: 0.75rem;
	margin-bottom: 0.5rem;
	border-radius: 0 8px 8px 0;
	font-size: 0.9rem;
}

.alert-item {
	background: #fff3cd;
	border-left-color: #ffc107;
	font-weight: 600;
}

.alert-item:contains("ALERT"), .alert-item:contains("WARNING") {
	background: #f8d7da;
	border-left-color: #dc3545;
}

.activity-summary {
	background: #f8f9ff;
	border-radius: 10px;
	padding: 1rem;
	border: 1px solid #e9ecef;
}

.activity-item {
	display: flex;
	align-items: center;
	margin-bottom: 0.5rem;
	font-size: 0.85rem;
}

.activity-item i {
	color: #6c757d;
}

.recommendations-container {
	max-height: 300px;
	overflow-y: auto;
}

.border-danger {
	border-color: #dc3545 !important;
	border-width: 2px !important;
}

.bg-danger {
	background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
}

.day-header {
	background: var(--gradient-primary);
	color: white;
	padding: 0.75rem 1rem;
	border-radius: 10px 10px 0 0;
	margin-bottom: 1rem;
}

.weather-details {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
	gap: 1rem;
	margin: 1rem 0;
}

.weather-detail-item {
	text-align: center;
	padding: 0.5rem;
	background: rgba(255,255,255,0.1);
	border-radius: 8px;
}
</style>

<script>
function getWeatherForecast() {
	const city = document.getElementById('cityInput').value.trim();
	
	if (!city) {
		showError('Please enter a city name');
		return;
	}
	
	// Show loading
	document.getElementById('loadingIndicator').style.display = 'block';
	document.getElementById('weatherResults').style.display = 'none';
	document.getElementById('errorMessage').style.display = 'none';
	
	// Fetch weather data
	fetch('/weather-forecast-api', {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
		},
		body: JSON.stringify({ city: city })
	})
	.then(response => response.json())
	.then(data => {
		document.getElementById('loadingIndicator').style.display = 'none';
		
		if (data.success) {
			displayWeatherResults(data);
		} else {
			showError(data.error || 'Unable to fetch weather data');
		}
	})
	.catch(error => {
		document.getElementById('loadingIndicator').style.display = 'none';
		showError('Network error. Please try again.');
		console.error('Error:', error);
	});
}

function displayWeatherResults(data) {
	const weatherResults = document.getElementById('weatherResults');
	const currentWeather = document.getElementById('currentWeather');
	const weeklyForecast = document.getElementById('weeklyForecast');

	// Display current weather
	const current = data.weather.current;
	currentWeather.innerHTML = `
		<div class="weather-card">
			<div class="row align-items-center">
				<div class="col-md-6">
					<h3><i class="fas fa-map-marker-alt me-2"></i>${data.weather.city}</h3>
					<div class="temp-display">${Math.round(current.main.temp)}°C</div>
					<p class="mb-0">${current.weather[0].description}</p>
				</div>
				<div class="col-md-6">
					<div class="weather-details">
						<div class="weather-detail-item">
							<i class="fas fa-eye"></i>
							<div>Feels like</div>
							<div>${Math.round(current.main.feels_like)}°C</div>
						</div>
						<div class="weather-detail-item">
							<i class="fas fa-tint"></i>
							<div>Humidity</div>
							<div>${current.main.humidity}%</div>
						</div>
						<div class="weather-detail-item">
							<i class="fas fa-wind"></i>
							<div>Wind</div>
							<div>${current.wind.speed} m/s</div>
						</div>
						<div class="weather-detail-item">
							<i class="fas fa-compress-arrows-alt"></i>
							<div>Pressure</div>
							<div>${current.main.pressure} hPa</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	`;
	
	// Display 7-day forecast
	weeklyForecast.innerHTML = '';
	data.recommendations.forEach((dayData, index) => {
		const day = dayData.date;
		const recommendations = dayData.recommendations;
		const activitySummary = dayData.activity_summary;
		const alertsCount = dayData.alerts_count;

		const dayCard = document.createElement('div');
		dayCard.className = 'col-lg-6 mb-4';

		const weatherIcon = getWeatherIcon(day.weather[0].main);
		const date = new Date(day.dt * 1000);
		const dayName = index === 0 ? 'Today' : date.toLocaleDateString('en-US', { weekday: 'long' });
		const dateStr = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });

		// Alert badge
		const alertBadge = alertsCount > 0 ? `<span class="badge bg-danger ms-2">${alertsCount} Alert${alertsCount > 1 ? 's' : ''}</span>` : '';

		// Activity status colors
		const getActivityColor = (status) => {
			switch(status) {
				case 'Excellent': return 'success';
				case 'Good': return 'warning';
				case 'Not Recommended': return 'danger';
				case 'Not Needed': return 'info';
				case 'High Priority': return 'danger';
				case 'Normal': return 'success';
				default: return 'secondary';
			}
		};

		dayCard.innerHTML = `
			<div class="forecast-card ${alertsCount > 0 ? 'border-danger' : ''}">
				<div class="day-header ${alertsCount > 0 ? 'bg-danger' : ''}">
					<div class="row align-items-center">
						<div class="col-6">
							<h5 class="mb-0">${dayName}${alertBadge}</h5>
							<small>${dateStr}</small>
						</div>
						<div class="col-6 text-end">
							<i class="${weatherIcon} fa-2x"></i>
							<div class="mt-1">
								<span class="fw-bold">${Math.round(day.temp.max)}°</span> /
								<span class="opacity-75">${Math.round(day.temp.min)}°</span>
							</div>
						</div>
					</div>
				</div>

				<div class="p-3">
					<div class="row mb-3">
						<div class="col-6">
							<small class="text-muted">Weather</small>
							<div class="fw-bold">${day.weather[0].description}</div>
						</div>
						<div class="col-6">
							<small class="text-muted">Humidity</small>
							<div class="fw-bold">${day.humidity}%</div>
						</div>
					</div>

					<!-- Activity Summary -->
					<div class="activity-summary mb-3">
						<h6 class="fw-bold mb-2">
							<i class="fas fa-tasks me-2"></i>Activity Suitability
						</h6>
						<div class="row g-2">
							<div class="col-6">
								<div class="activity-item">
									<i class="fas fa-seedling me-1"></i>
									<small>Planting:</small>
									<span class="badge bg-${getActivityColor(activitySummary.planting)} ms-1">${activitySummary.planting}</span>
								</div>
							</div>
							<div class="col-6">
								<div class="activity-item">
									<i class="fas fa-wheat-awn me-1"></i>
									<small>Harvesting:</small>
									<span class="badge bg-${getActivityColor(activitySummary.harvesting)} ms-1">${activitySummary.harvesting}</span>
								</div>
							</div>
							<div class="col-6">
								<div class="activity-item">
									<i class="fas fa-spray-can me-1"></i>
									<small>Spraying:</small>
									<span class="badge bg-${getActivityColor(activitySummary.spraying)} ms-1">${activitySummary.spraying}</span>
								</div>
							</div>
							<div class="col-6">
								<div class="activity-item">
									<i class="fas fa-tint me-1"></i>
									<small>Irrigation:</small>
									<span class="badge bg-${getActivityColor(activitySummary.irrigation)} ms-1">${activitySummary.irrigation}</span>
								</div>
							</div>
						</div>
					</div>

					<!-- Detailed Recommendations -->
					<h6 class="fw-bold text-success mb-2">
						<i class="fas fa-clipboard-list me-2"></i>Detailed Recommendations
					</h6>
					<div class="recommendations-container">
						${recommendations.map(rec => `
							<div class="recommendation-item ${rec.includes('ALERT') || rec.includes('WARNING') ? 'alert-item' : ''}">
								${rec}
							</div>
						`).join('')}
					</div>
				</div>
			</div>
		`;

		weeklyForecast.appendChild(dayCard);
	});
	
	weatherResults.style.display = 'block';
}

function getWeatherIcon(weatherMain) {
	const icons = {
		'Clear': 'fas fa-sun text-warning',
		'Clouds': 'fas fa-cloud text-secondary',
		'Rain': 'fas fa-cloud-rain text-primary',
		'Drizzle': 'fas fa-cloud-drizzle text-info',
		'Thunderstorm': 'fas fa-bolt text-warning',
		'Snow': 'fas fa-snowflake text-light',
		'Mist': 'fas fa-smog text-secondary',
		'Fog': 'fas fa-smog text-secondary'
	};
	return icons[weatherMain] || 'fas fa-cloud text-secondary';
}

function showError(message) {
	document.getElementById('errorText').textContent = message;
	document.getElementById('errorMessage').style.display = 'block';
}

function handleKeyPress(event) {
	if (event.key === 'Enter') {
		getWeatherForecast();
	}
}

// Focus on input when page loads
document.addEventListener('DOMContentLoaded', function() {
	document.getElementById('cityInput').focus();
});
</script>

{% endblock %}
