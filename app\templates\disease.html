{% extends 'layout.html' %}

{% block body %}
<!-- <PERSON> Header -->
<div class="page-header">
	<div class="container">
		<h1 data-aos="fade-up">
			<i class="fas fa-microscope me-3"></i>Disease Detection AI
		</h1>
		<p data-aos="fade-up" data-aos-delay="200">
			Upload plant images for instant disease identification using advanced computer vision and get treatment recommendations
		</p>
	</div>
</div>

<!-- Main Content -->
<div class="container py-5">
	<div class="row justify-content-center">
		<div class="col-lg-8">
			<!-- Info Cards -->
			<div class="row mb-5">
				<div class="col-md-4 mb-3" data-aos="fade-up" data-aos-delay="100">
					<div class="card-modern text-center p-3">
						<i class="fas fa-camera text-info fa-2x mb-2"></i>
						<h6 class="fw-bold">Image Recognition</h6>
						<small class="text-muted">Advanced computer vision technology</small>
					</div>
				</div>
				<div class="col-md-4 mb-3" data-aos="fade-up" data-aos-delay="200">
					<div class="card-modern text-center p-3">
						<i class="fas fa-bolt text-warning fa-2x mb-2"></i>
						<h6 class="fw-bold">Instant Results</h6>
						<small class="text-muted">Get diagnosis in seconds</small>
					</div>
				</div>
				<div class="col-md-4 mb-3" data-aos="fade-up" data-aos-delay="300">
					<div class="card-modern text-center p-3">
						<i class="fas fa-stethoscope text-success fa-2x mb-2"></i>
						<h6 class="fw-bold">Treatment Plans</h6>
						<small class="text-muted">Actionable recommendations</small>
					</div>
				</div>
			</div>

			<!-- Upload Form -->
			<div class="form-modern" data-aos="fade-up" data-aos-delay="400">
				<div class="text-center mb-4">
					<h3 class="text-gradient fw-bold">Upload Plant Image</h3>
					<p class="text-muted">Take a clear photo of the affected plant leaf or upload an existing image for analysis</p>
				</div>

				<form method="post" enctype="multipart/form-data" class="text-center">
					<!-- File Upload Area -->
					<div class="upload-area mb-4" id="uploadArea">
						<div class="upload-content">
							<i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
							<h5 class="fw-bold mb-2">Drag & Drop Your Image Here</h5>
							<p class="text-muted mb-3">or click to browse files</p>
							<input type="file" name="file" id="inputfile" accept="image/*" 
								   onchange="preview_image(event)" class="d-none" required>
							<button type="button" class="btn btn-outline-primary" onclick="document.getElementById('inputfile').click()">
								<i class="fas fa-folder-open me-2"></i>Choose Image
							</button>
						</div>
					</div>

					<!-- Image Preview -->
					<div id="imagePreview" class="mb-4" style="display: none;">
						<div class="position-relative d-inline-block">
							<img id="output-image" class="img-fluid rounded shadow" style="max-width: 400px; max-height: 300px;" />
							<button type="button" class="btn btn-sm btn-danger position-absolute top-0 end-0 m-2 rounded-circle" 
									onclick="removeImage()" style="width: 30px; height: 30px;">
								<i class="fas fa-times"></i>
							</button>
						</div>
						<div class="mt-3">
							<small class="text-muted">Image uploaded successfully. Click predict to analyze.</small>
						</div>
					</div>

					<!-- Submit Button -->
					<div class="text-center">
						<button type="submit" class="btn btn-modern btn-lg px-5" id="predictBtn" disabled>
							<i class="fas fa-search me-2"></i>Analyze Image
						</button>
					</div>
				</form>
			</div>

			<!-- Supported Diseases -->
			<div class="mt-5" data-aos="fade-up">
				<h4 class="text-center mb-4 fw-bold">Detectable Plant Diseases</h4>
				<div class="row">
					<div class="col-md-6 mb-3">
						<div class="d-flex align-items-center">
							<div class="bg-danger bg-opacity-10 rounded-circle p-2 me-3">
								<i class="fas fa-bug text-danger"></i>
							</div>
							<div>
								<h6 class="fw-bold mb-1">Fungal Diseases</h6>
								<small class="text-muted">Leaf spots, blights, rusts, and mildews</small>
							</div>
						</div>
					</div>
					<div class="col-md-6 mb-3">
						<div class="d-flex align-items-center">
							<div class="bg-warning bg-opacity-10 rounded-circle p-2 me-3">
								<i class="fas fa-virus text-warning"></i>
							</div>
							<div>
								<h6 class="fw-bold mb-1">Viral Infections</h6>
								<small class="text-muted">Mosaic viruses and leaf curl diseases</small>
							</div>
						</div>
					</div>
					<div class="col-md-6 mb-3">
						<div class="d-flex align-items-center">
							<div class="bg-info bg-opacity-10 rounded-circle p-2 me-3">
								<i class="fas fa-bacteria text-info"></i>
							</div>
							<div>
								<h6 class="fw-bold mb-1">Bacterial Diseases</h6>
								<small class="text-muted">Bacterial spots and wilts</small>
							</div>
						</div>
					</div>
					<div class="col-md-6 mb-3">
						<div class="d-flex align-items-center">
							<div class="bg-success bg-opacity-10 rounded-circle p-2 me-3">
								<i class="fas fa-check-circle text-success"></i>
							</div>
							<div>
								<h6 class="fw-bold mb-1">Healthy Plants</h6>
								<small class="text-muted">Identification of healthy plant conditions</small>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- Tips -->
			<div class="mt-5" data-aos="fade-up">
				<div class="alert alert-info border-0 rounded-3">
					<h6 class="fw-bold mb-2">
						<i class="fas fa-lightbulb me-2"></i>Tips for Best Results
					</h6>
					<ul class="mb-0 small">
						<li>Use clear, well-lit images of affected plant parts</li>
						<li>Focus on leaves showing disease symptoms</li>
						<li>Avoid blurry or dark images</li>
						<li>Include the entire leaf in the frame when possible</li>
					</ul>
				</div>
			</div>
		</div>
	</div>
</div>

<style>
.upload-area {
	border: 2px dashed #007bff;
	border-radius: 15px;
	padding: 3rem 2rem;
	background: #f8f9ff;
	transition: all 0.3s ease;
	cursor: pointer;
}

.upload-area:hover {
	border-color: #0056b3;
	background: #e7f1ff;
}

.upload-area.dragover {
	border-color: #28a745;
	background: #e8f5e9;
}
</style>

<script>
function preview_image(event) {
	const file = event.target.files[0];
	if (file) {
		const reader = new FileReader();
		reader.onload = function() {
			const output = document.getElementById('output-image');
			const preview = document.getElementById('imagePreview');
			const uploadArea = document.getElementById('uploadArea');
			const predictBtn = document.getElementById('predictBtn');
			
			output.src = reader.result;
			preview.style.display = 'block';
			uploadArea.style.display = 'none';
			predictBtn.disabled = false;
		}
		reader.readAsDataURL(file);
	}
}

function removeImage() {
	const preview = document.getElementById('imagePreview');
	const uploadArea = document.getElementById('uploadArea');
	const predictBtn = document.getElementById('predictBtn');
	const fileInput = document.getElementById('inputfile');
	
	preview.style.display = 'none';
	uploadArea.style.display = 'block';
	predictBtn.disabled = true;
	fileInput.value = '';
}

// Drag and drop functionality
const uploadArea = document.getElementById('uploadArea');

uploadArea.addEventListener('dragover', (e) => {
	e.preventDefault();
	uploadArea.classList.add('dragover');
});

uploadArea.addEventListener('dragleave', () => {
	uploadArea.classList.remove('dragover');
});

uploadArea.addEventListener('drop', (e) => {
	e.preventDefault();
	uploadArea.classList.remove('dragover');
	
	const files = e.dataTransfer.files;
	if (files.length > 0) {
		const fileInput = document.getElementById('inputfile');
		fileInput.files = files;
		preview_image({ target: { files: files } });
	}
});

uploadArea.addEventListener('click', () => {
	document.getElementById('inputfile').click();
});
</script>

{% endblock %}
