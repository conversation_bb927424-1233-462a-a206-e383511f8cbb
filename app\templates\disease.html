{% extends 'layout.html' %}

{% block body %}
<!-- Page Header -->
<div class="page-header">
	<div class="container">
		<h1 data-aos="fade-up">
			<i class="fas fa-microscope me-3"></i>Plant Disease Detection
		</h1>
		<p data-aos="fade-up" data-aos-delay="200">
			Upload an image of your plant leaf to detect diseases and get treatment recommendations using AI-powered analysis
		</p>
	</div>
</div>

<!-- Main Content -->
<div class="container py-5">
	<div class="row justify-content-center">
		<div class="col-lg-8">
			<!-- Error Message -->
			{% if error %}
			<div class="alert alert-danger alert-dismissible fade show" role="alert" data-aos="fade-up">
				<i class="fas fa-exclamation-triangle me-2"></i>
				{{ error }}
				<button type="button" class="btn-close" data-bs-dismiss="alert"></button>
			</div>
			{% endif %}

			<!-- Upload Form -->
			<div class="card-modern p-5" data-aos="fade-up" data-aos-delay="200">
				<div class="text-center mb-4">
					<div class="feature-icon mb-3">
						<div class="bg-danger bg-opacity-10 rounded-circle p-4 d-inline-block">
							<i class="fas fa-leaf text-danger" style="font-size: 3rem;"></i>
						</div>
					</div>
					<h3 class="fw-bold mb-3">Disease Detection AI</h3>
					<p class="text-muted">
						Our advanced AI can identify plant diseases from leaf images and provide detailed treatment recommendations.
					</p>
				</div>

				<form method="POST" enctype="multipart/form-data" id="diseaseForm">
					<div class="mb-4">
						<label for="file" class="form-label fw-bold">
							<i class="fas fa-upload me-2"></i>Upload Plant Image
						</label>
						<div class="upload-area" id="uploadArea">
							<input type="file" class="form-control" id="file" name="file" accept="image/*" required>
							<div class="upload-placeholder" id="uploadPlaceholder">
								<i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
								<h5>Click to upload or drag and drop</h5>
								<p class="text-muted">PNG, JPG, JPEG, GIF, BMP (Max 10MB)</p>
							</div>
							<div class="upload-preview" id="uploadPreview" style="display: none;">
								<img id="previewImage" src="" alt="Preview" class="img-fluid rounded">
								<div class="mt-2">
									<button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearImage()">
										<i class="fas fa-times me-1"></i>Remove
									</button>
								</div>
							</div>
						</div>
					</div>

					<div class="text-center">
						<button type="submit" class="btn btn-modern btn-lg px-5" id="submitBtn">
							<i class="fas fa-search me-2"></i>Analyze Plant Disease
						</button>
					</div>
				</form>

				<!-- Loading Indicator -->
				<div class="text-center mt-4" id="loadingIndicator" style="display: none;">
					<div class="spinner-border text-primary" role="status">
						<span class="visually-hidden">Analyzing...</span>
					</div>
					<p class="mt-2 text-muted">Analyzing your plant image...</p>
				</div>
			</div>

			<!-- Information Cards -->
			<div class="row mt-5">
				<div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="300">
					<div class="info-card text-center p-4">
						<i class="fas fa-brain fa-2x text-primary mb-3"></i>
						<h5 class="fw-bold">AI-Powered</h5>
						<p class="text-muted">Advanced deep learning models trained on thousands of plant disease images</p>
					</div>
				</div>
				<div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="400">
					<div class="info-card text-center p-4">
						<i class="fas fa-clock fa-2x text-success mb-3"></i>
						<h5 class="fw-bold">Instant Results</h5>
						<p class="text-muted">Get disease identification and treatment recommendations in seconds</p>
					</div>
				</div>
				<div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="500">
					<div class="info-card text-center p-4">
						<i class="fas fa-shield-alt fa-2x text-warning mb-3"></i>
						<h5 class="fw-bold">Accurate Detection</h5>
						<p class="text-muted">High accuracy disease detection for multiple crop types and conditions</p>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<style>
.upload-area {
	border: 2px dashed #ddd;
	border-radius: 15px;
	padding: 2rem;
	text-align: center;
	transition: all 0.3s ease;
	position: relative;
	background: #fafafa;
}

.upload-area:hover {
	border-color: var(--primary-green);
	background: #f0f8f0;
}

.upload-area.dragover {
	border-color: var(--primary-green);
	background: #e8f5e8;
	transform: scale(1.02);
}

.upload-area input[type="file"] {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	opacity: 0;
	cursor: pointer;
}

.upload-preview img {
	max-height: 300px;
	border-radius: 10px;
	box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.info-card {
	background: white;
	border-radius: 15px;
	box-shadow: 0 4px 6px rgba(0,0,0,0.1);
	transition: all 0.3s ease;
	border: 1px solid #f0f0f0;
}

.info-card:hover {
	transform: translateY(-5px);
	box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

#loadingIndicator {
	animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
	from { opacity: 0; }
	to { opacity: 1; }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
	const fileInput = document.getElementById('file');
	const uploadArea = document.getElementById('uploadArea');
	const uploadPlaceholder = document.getElementById('uploadPlaceholder');
	const uploadPreview = document.getElementById('uploadPreview');
	const previewImage = document.getElementById('previewImage');
	const form = document.getElementById('diseaseForm');
	const submitBtn = document.getElementById('submitBtn');
	const loadingIndicator = document.getElementById('loadingIndicator');

	// File input change handler
	fileInput.addEventListener('change', function(e) {
		handleFileSelect(e.target.files[0]);
	});

	// Drag and drop handlers
	uploadArea.addEventListener('dragover', function(e) {
		e.preventDefault();
		uploadArea.classList.add('dragover');
	});

	uploadArea.addEventListener('dragleave', function(e) {
		e.preventDefault();
		uploadArea.classList.remove('dragover');
	});

	uploadArea.addEventListener('drop', function(e) {
		e.preventDefault();
		uploadArea.classList.remove('dragover');
		const files = e.dataTransfer.files;
		if (files.length > 0) {
			fileInput.files = files;
			handleFileSelect(files[0]);
		}
	});

	// Form submit handler
	form.addEventListener('submit', function(e) {
		if (!fileInput.files[0]) {
			e.preventDefault();
			alert('Please select an image file first.');
			return;
		}
		
		submitBtn.disabled = true;
		submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Analyzing...';
		loadingIndicator.style.display = 'block';
	});

	function handleFileSelect(file) {
		if (file && file.type.startsWith('image/')) {
			const reader = new FileReader();
			reader.onload = function(e) {
				previewImage.src = e.target.result;
				uploadPlaceholder.style.display = 'none';
				uploadPreview.style.display = 'block';
			};
			reader.readAsDataURL(file);
		}
	}
});

function clearImage() {
	const fileInput = document.getElementById('file');
	const uploadPlaceholder = document.getElementById('uploadPlaceholder');
	const uploadPreview = document.getElementById('uploadPreview');
	
	fileInput.value = '';
	uploadPlaceholder.style.display = 'block';
	uploadPreview.style.display = 'none';
}
</script>

{% endblock %}
